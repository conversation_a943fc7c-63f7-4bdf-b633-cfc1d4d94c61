import { useState } from 'react';
import { markDayCompleted, saveReadingPlan } from '../utils/quranPlanUtils';

// Add CSS animations
const animationStyles = `
  @keyframes bounce {
    0%, 20%, 60%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    80% {
      transform: translateY(-5px);
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = animationStyles;
  document.head.appendChild(styleSheet);
}

function ReadingTaskDisplay({ readingTask, planData, onTaskCompleted, onShowSchedule }) {
  const [isCompleting, setIsCompleting] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);

  const handleMarkCompleted = async () => {
    if (!readingTask || !planData) return;

    setIsCompleting(true);
    setShowAnimation(true);

    try {
      // Add a small delay for animation effect
      await new Promise(resolve => setTimeout(resolve, 800));

      const updatedPlan = markDayCompleted(planData, readingTask.dayNumber);
      const saved = saveReadingPlan(updatedPlan);

      if (saved) {
        onTaskCompleted(updatedPlan);
      } else {
        alert('Failed to save progress. Please try again.');
        setShowAnimation(false);
      }
    } catch (error) {
      console.error('Error marking task as completed:', error);
      alert('Failed to save progress. Please try again.');
      setShowAnimation(false);
    } finally {
      setIsCompleting(false);
    }
  };

  const isTaskCompleted = () => {
    if (!planData || !planData.completedDays || !readingTask) return false;
    return planData.completedDays[readingTask.dayNumber - 1] === true;
  };

  if (!readingTask) {
    return (
      <div style={{
        backgroundColor: '#FFF8E1',
        padding: '20px',
        borderRadius: '10px',
        border: '2px solid #d4af37',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h4 style={{ color: '#8B4513', marginBottom: '10px' }}>
          No Reading Task Available
        </h4>
        <p style={{ color: '#666', fontSize: '14px' }}>
          Your reading plan may be completed or not yet started.
        </p>
      </div>
    );
  }

  const completed = isTaskCompleted();

  return (
    <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center', // optional: vertical center
    width: '100%',
    marginTop: '20px' // adjust as needed
  }}>
    <div style={{
      backgroundColor: completed ? '#E8F5E8' : '#FFF8E1',
      padding: '20px',
      borderRadius: '10px',
      border: `2px solid ${completed ? '#4CAF50' : '#d4af37'}`,
      maxWidth: '400px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      width: '100%', // ensure responsiveness
    }}>
        <div>
          <h4 style={{
            color: completed ? '#2E7D32' : '#8B4513',
            marginBottom: '5px',
            fontSize: '18px',
            textAlign: 'center'
          }}>
            Day {readingTask.dayNumber} Reading Task
          </h4>
          {completed && (
           <div style={{ textAlign: 'center', width: '100%' }}>
  <span style={{
    color: '#4CAF50',
    fontSize: '12px',
    fontWeight: 'bold',
    backgroundColor: '#C8E6C9',
    padding: '2px 8px',
    borderRadius: '12px',
    marginBottom: '8px',
    display: 'inline-block' // ensures proper centering
  }}>
    ✓ COMPLETED
  </span>
</div>

          )}
        </div>
        
        {!completed && (
        <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
  <button
    onClick={handleMarkCompleted}
    disabled={isCompleting}
    style={{
      backgroundColor: showAnimation ? '#4CAF50' : '#d4af37',
      color: 'white',
      border: 'none',
      borderRadius: '20px',
      padding: '8px 20px',
      cursor: isCompleting ? 'not-allowed' : 'pointer',
      fontSize: '13px',
      fontWeight: 'bold',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '6px',
      lineHeight: 1,
      height: '30px',
      opacity: isCompleting ? 0.8 : 1,
      transition: 'all 0.3s ease',
      boxShadow: showAnimation
        ? '0 4px 20px rgba(76, 175, 80, 0.4)'
        : '0 2px 8px rgba(212, 175, 55, 0.3)',
      transform: showAnimation ? 'scale(1.05)' : 'scale(1)'
    }}
    onMouseEnter={(e) => {
      if (!isCompleting && !showAnimation) {
        e.target.style.backgroundColor = '#bfa131';
        e.target.style.transform = 'scale(1.05)';
      }
    }}
    onMouseLeave={(e) => {
      if (!isCompleting && !showAnimation) {
        e.target.style.backgroundColor = '#d4af37';
        e.target.style.transform = 'scale(1)';
      }
    }}
    title="Mark today's task as completed"
  >
    {showAnimation ? (
      <>
        <span style={{
          fontSize: '16px',
          animation: 'bounce 0.6s ease-in-out'
        }}>✓</span>
        <span>Completed!</span>
      </>
    ) : isCompleting ? (
      <>
        <span style={{
          animation: 'spin 1s linear infinite',
          display: 'inline-block'
        }}>⟳</span>
        <span>Completing...</span>
      </>
    ) : (
      <>
        <span>📖</span>
        <span>Today's Task Completed</span>
      </>
    )}
  </button>
</div>
        )}
      </div>

      <div style={{
        backgroundColor: 'white',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '15px',
        marginLeft: '15px',
        border: '1px solid #e0e0e0'
      }}>
        <p style={{
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#333',
          marginBottom: '8px',
          textAlign: 'center'
        }}>
          Read from Page {readingTask.startPage} to {readingTask.endPage}
        </p>
        <p style={{
          fontSize: '14px',
          color: '#666',
          textAlign: 'center',
          margin: 0
        }}>
          Total: {readingTask.pagesCount} pages
        </p>
      </div>

      {readingTask.hasMissedDays && (
        <div style={{
          backgroundColor: '#FFEBEE',
          padding: '10px',
          borderRadius: '6px',
          border: '1px solid #FFCDD2',
          marginBottom: '15px'
        }}>
          <p style={{
            fontSize: '12px',
            color: '#C62828',
            margin: 0,
            textAlign: 'center'
          }}>
            ⚠️ Includes {readingTask.missedDaysCount} missed day{readingTask.missedDaysCount > 1 ? 's' : ''}
          </p>
        </div>
      )}

    </div>
  );
}

export default ReadingTaskDisplay;
