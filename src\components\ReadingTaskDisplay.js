import { useState } from 'react';
import { markDayCompleted, saveReadingPlan } from '../utils/quranPlanUtils';

function ReadingTaskDisplay({ readingTask, planData, onTaskCompleted, onShowSchedule }) {
  const [isCompleting, setIsCompleting] = useState(false);

  const handleMarkCompleted = async () => {
    if (!readingTask || !planData) return;

    setIsCompleting(true);
    
    try {
      const updatedPlan = markDayCompleted(planData, readingTask.dayNumber);
      const saved = saveReadingPlan(updatedPlan);
      
      if (saved) {
        onTaskCompleted(updatedPlan);
      } else {
        alert('Failed to save progress. Please try again.');
      }
    } catch (error) {
      console.error('Error marking task as completed:', error);
      alert('Failed to save progress. Please try again.');
    } finally {
      setIsCompleting(false);
    }
  };

  const isTaskCompleted = () => {
    if (!planData || !planData.completedDays || !readingTask) return false;
    return planData.completedDays[readingTask.dayNumber - 1] === true;
  };

  if (!readingTask) {
    return (
      <div style={{
        backgroundColor: '#FFF8E1',
        padding: '20px',
        borderRadius: '10px',
        border: '2px solid #d4af37',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h4 style={{ color: '#8B4513', marginBottom: '10px' }}>
          No Reading Task Available
        </h4>
        <p style={{ color: '#666', fontSize: '14px' }}>
          Your reading plan may be completed or not yet started.
        </p>
      </div>
    );
  }

  const completed = isTaskCompleted();

  return (
    <div style={{
      backgroundColor: completed ? '#E8F5E8' : '#FFF8E1',
      padding: '20px',
      borderRadius: '10px',
      border: `2px solid ${completed ? '#4CAF50' : '#d4af37'}`,
      maxWidth: '400px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: '15px'
      }}>
        <div>
          <h4 style={{
            color: completed ? '#2E7D32' : '#8B4513',
            marginBottom: '5px',
            fontSize: '18px'
          }}>
            Day {readingTask.dayNumber} Reading Task
          </h4>
          {completed && (
            <span style={{
              color: '#4CAF50',
              fontSize: '12px',
              fontWeight: 'bold',
              backgroundColor: '#C8E6C9',
              padding: '2px 8px',
              borderRadius: '12px'
            }}>
              ✓ COMPLETED
            </span>
          )}
        </div>
        
        {!completed && (
          <button
            onClick={handleMarkCompleted}
            disabled={isCompleting}
            style={{
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              cursor: isCompleting ? 'not-allowed' : 'pointer',
              fontSize: '18px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: isCompleting ? 0.6 : 1,
              transition: 'all 0.3s ease',
              boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)'
            }}
            onMouseEnter={(e) => {
              if (!isCompleting) {
                e.target.style.backgroundColor = '#45a049';
                e.target.style.transform = 'scale(1.1)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isCompleting) {
                e.target.style.backgroundColor = '#4CAF50';
                e.target.style.transform = 'scale(1)';
              }
            }}
            title="Mark as completed"
          >
            {isCompleting ? '...' : '✓'}
          </button>
        )}
      </div>

      <div style={{
        backgroundColor: 'white',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '15px',
        border: '1px solid #e0e0e0'
      }}>
        <p style={{
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#333',
          marginBottom: '8px',
          textAlign: 'center'
        }}>
          Read from Page {readingTask.startPage} to {readingTask.endPage}
        </p>
        <p style={{
          fontSize: '14px',
          color: '#666',
          textAlign: 'center',
          margin: 0
        }}>
          Total: {readingTask.pagesCount} pages
        </p>
      </div>

      {readingTask.hasMissedDays && (
        <div style={{
          backgroundColor: '#FFEBEE',
          padding: '10px',
          borderRadius: '6px',
          border: '1px solid #FFCDD2',
          marginBottom: '15px'
        }}>
          <p style={{
            fontSize: '12px',
            color: '#C62828',
            margin: 0,
            textAlign: 'center'
          }}>
            ⚠️ Includes {readingTask.missedDaysCount} missed day{readingTask.missedDaysCount > 1 ? 's' : ''}
          </p>
        </div>
      )}

      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '10px'
      }}>
        <button
          onClick={onShowSchedule}
          style={{
            padding: '8px 16px',
            borderRadius: '6px',
            border: '1px solid #d4af37',
            backgroundColor: 'white',
            color: '#8B4513',
            fontSize: '14px',
            cursor: 'pointer',
            fontWeight: 'bold',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = '#FFF8E1';
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = 'white';
          }}
        >
          📅 Schedule
        </button>
      </div>

      <div style={{
        marginTop: '15px',
        padding: '10px',
        backgroundColor: 'rgba(212, 175, 55, 0.1)',
        borderRadius: '6px',
        textAlign: 'center'
      }}>
        <p style={{
          fontSize: '12px',
          color: '#8B4513',
          margin: 0,
          fontStyle: 'italic'
        }}>
          Reading direction: Right to Left (Arabic style)
        </p>
      </div>
    </div>
  );
}

export default ReadingTaskDisplay;
