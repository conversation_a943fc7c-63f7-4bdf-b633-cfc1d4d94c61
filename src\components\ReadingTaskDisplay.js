import { useState } from 'react';
import { markDayCompleted, saveReadingPlan, calculateCompletionPercentage, completeAndArchivePlan } from '../utils/quranPlanUtils';
import { getStoredUserData, updateReadingProgress, completeReadingPlan } from '../utils/userApi';

// Add CSS animations
const animationStyles = `
  @keyframes bounce {
    0%, 20%, 60%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    80% {
      transform: translateY(-5px);
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes congratulations {
    0% {
      transform: scale(0) rotate(0deg);
      opacity: 0;
    }
    50% {
      transform: scale(1.2) rotate(180deg);
      opacity: 1;
    }
    100% {
      transform: scale(1) rotate(360deg);
      opacity: 1;
    }
  }

  @keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-20px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-20px); }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = animationStyles;
  document.head.appendChild(styleSheet);
}

function ReadingTaskDisplay({ readingTask, planData, onTaskCompleted, onShowSchedule }) {
  const [isCompleting, setIsCompleting] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);

  const handleMarkCompleted = async () => {
    if (!readingTask || !planData) return;

    setIsCompleting(true);
    setShowAnimation(true);

    try {
      // Add a small delay for animation effect
      await new Promise(resolve => setTimeout(resolve, 800));

      // Get user data for backend calls
      const userData = getStoredUserData();

      // Update local plan
      const updatedPlan = markDayCompleted(planData, readingTask.dayNumber);
      const saved = saveReadingPlan(updatedPlan);

      if (!saved) {
        alert('Failed to save progress locally. Please try again.');
        setShowAnimation(false);
        setIsCompleting(false);
        return;
      }

      // Send progress to backend
      if (userData && userData.id && planData && planData.taskId) {
        try {
          const taskData = {
            userId: userData.id,
            dayNumber: readingTask.dayNumber,
            startPage: readingTask.startPage,
            endPage: readingTask.endPage,
            progress: readingTask.pagesCount,
            surahRange: "" // You can add surah range calculation if needed
          };

          console.log('📤 Sending progress to server:', {
            taskId: planData.taskId,
            taskData
          });

          await updateReadingProgress(planData.taskId, taskData);
          console.log('✅ Progress updated on server');
        } catch (serverError) {
          console.error('⚠️ Failed to update progress on server:', serverError);
          console.error('📋 Server error details:', {
            message: serverError.message,
            taskId: planData.taskId,
            taskData: {
              userId: userData.id,
              dayNumber: readingTask.dayNumber,
              progress: readingTask.pagesCount
            }
          });
          // Continue with local update even if server fails
        }
      } else {
        console.log('⚠️ No task ID available for server update:', {
          hasUserData: !!userData,
          hasPlanData: !!planData,
          hasTaskId: !!(planData && planData.taskId)
        });
      }

      // Check if plan is completed
      const completionPercentage = calculateCompletionPercentage(updatedPlan);
      console.log('📊 Current completion percentage:', completionPercentage);

      if (completionPercentage >= 100) {
        console.log('🎉 Plan completed! Showing celebration...');

        // Plan is completed - show congratulations and notify server
        setShowCongratulations(true);

        if (userData && userData.id && planData && planData.taskId) {
          try {
            const completionTaskData = {
              userId: userData.id,
              dayNumber: readingTask.dayNumber,
              startPage: readingTask.startPage,
              endPage: readingTask.endPage,
              progress: readingTask.pagesCount,
              surahRange: "" // You can add surah range calculation if needed
            };

            await completeReadingPlan(planData.taskId, completionTaskData);
            console.log('✅ Plan completion notified to server');
          } catch (serverError) {
            console.error('⚠️ Failed to notify server of plan completion:', serverError);
          }
        } else {
          console.log('⚠️ No task ID available for completion notification');
        }

        // Show congratulations for 4 seconds, then auto-archive plan
        setTimeout(() => {
          setShowCongratulations(false);

          // Archive the completed plan and move to summary
          console.log('📚 Archiving completed plan...');

          const archived = completeAndArchivePlan(updatedPlan);
          if (archived) {
            console.log('✅ Plan successfully archived');
            // Notify parent component that plan is completed and should be removed
            onTaskCompleted(null); // Pass null to indicate plan should be removed
          } else {
            console.error('❌ Failed to archive plan');
            // Fallback: just remove from current plan
            localStorage.removeItem('readingPlan');
            onTaskCompleted(null);
          }

        }, 4000);
      }

      onTaskCompleted(updatedPlan);

    } catch (error) {
      console.error('Error marking task as completed:', error);
      alert('Failed to save progress. Please try again.');
      setShowAnimation(false);
    } finally {
      setIsCompleting(false);
    }
  };

  const isTaskCompleted = () => {
    if (!planData || !planData.completedDays || !readingTask) return false;
    return planData.completedDays[readingTask.dayNumber - 1] === true;
  };

  if (!readingTask) {
    return (
      <div style={{
        backgroundColor: '#FFF8E1',
        padding: '20px',
        borderRadius: '10px',
        border: '2px solid #d4af37',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h4 style={{ color: '#8B4513', marginBottom: '10px' }}>
          No Reading Task Available
        </h4>
        <p style={{ color: '#666', fontSize: '14px' }}>
          Your reading plan may be completed or not yet started.
        </p>
      </div>
    );
  }

  const completed = isTaskCompleted();

  return (
    <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center', // optional: vertical center
    width: '100%',
    marginTop: '20px' // adjust as needed
  }}>
    <div style={{
      backgroundColor: completed ? '#E8F5E8' : '#FFF8E1',
      padding: '20px',
      borderRadius: '10px',
      border: `2px solid ${completed ? '#4CAF50' : '#d4af37'}`,
      maxWidth: '400px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      width: '100%', // ensure responsiveness
    }}>
        <div>
          <h4 style={{
            color: completed ? '#2E7D32' : '#8B4513',
            marginBottom: '5px',
            fontSize: '18px',
            textAlign: 'center'
          }}>
            Day {readingTask.dayNumber} Reading Task
          </h4>
          {completed && (
           <div style={{ textAlign: 'center', width: '100%' }}>
  <span style={{
    color: '#4CAF50',
    fontSize: '12px',
    fontWeight: 'bold',
    backgroundColor: '#C8E6C9',
    padding: '2px 8px',
    borderRadius: '12px',
    marginBottom: '8px',
    display: 'inline-block' // ensures proper centering
  }}>
    ✓ COMPLETED
  </span>
</div>

          )}
        </div>
        
        {!completed && (
          <div style={{ width: '100%', display: 'flex', justifyContent: 'center', position: 'relative' }}>
            <button
              onClick={handleMarkCompleted}
              disabled={isCompleting}
              style={{
                backgroundColor: showAnimation ? '#4CAF50' : '#d4af37',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '10px 24px',
                cursor: isCompleting ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                lineHeight: 1,
                minHeight: '40px',
                opacity: isCompleting ? 0.8 : 1,
                transition: 'all 0.3s ease',
                boxShadow: showAnimation
                  ? '0 4px 20px rgba(76, 175, 80, 0.4)'
                  : '0 2px 8px rgba(212, 175, 55, 0.2)',
                transform: showAnimation ? 'scale(1.05)' : 'scale(1)',
                outline: 'none'
              }}
              onMouseEnter={(e) => {
                if (!isCompleting && !showAnimation) {
                  e.target.style.backgroundColor = '#bfa131';
                  e.target.style.transform = 'scale(1.02)';
                  e.target.style.boxShadow = '0 4px 12px rgba(212, 175, 55, 0.3)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isCompleting && !showAnimation) {
                  e.target.style.backgroundColor = '#d4af37';
                  e.target.style.transform = 'scale(1)';
                  e.target.style.boxShadow = '0 2px 8px rgba(212, 175, 55, 0.2)';
                }
              }}
              title="Mark today's task as completed"
            >
              {showAnimation ? (
                <>
                  <span style={{
                    fontSize: '16px',
                    animation: 'bounce 0.6s ease-in-out'
                  }}>✓</span>
                  <span>Completed!</span>
                </>
              ) : isCompleting ? (
                <>
                  <span style={{
                    animation: 'spin 1s linear infinite',
                    display: 'inline-block'
                  }}>⟳</span>
                  <span>Completing...</span>
                </>
              ) : (
                <span>Mark as Completed</span>
              )}
            </button>

            {/* Congratulations Animation Overlay */}
            {showCongratulations && (
              <div style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 9999,
                animation: 'fadeInOut 3s ease-in-out'
              }}>
                <div style={{
                  textAlign: 'center',
                  color: 'white'
                }}>
                  <div style={{
                    fontSize: '80px',
                    animation: 'congratulations 1s ease-in-out',
                    marginBottom: '20px'
                  }}>
                    🎉
                  </div>
                  <h2 style={{
                    fontSize: '32px',
                    fontWeight: 'bold',
                    margin: '0 0 10px 0',
                    color: '#FFD700'
                  }}>
                    Congratulations!
                  </h2>
                  <p style={{
                    fontSize: '18px',
                    margin: 0,
                    opacity: 0.9
                  }}>
                    You have completed your Quran reading plan! 🤲
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div style={{
        backgroundColor: 'white',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '15px',
        marginLeft: '15px',
        border: '1px solid #e0e0e0'
      }}>
        <p style={{
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#333',
          marginBottom: '8px',
          textAlign: 'center'
        }}>
          Read from Page {readingTask.startPage} to {readingTask.endPage}
        </p>
        <p style={{
          fontSize: '14px',
          color: '#666',
          textAlign: 'center',
          margin: 0
        }}>
          Total: {readingTask.pagesCount} pages
        </p>
      </div>

      {readingTask.hasMissedDays && (
        <div style={{
          backgroundColor: '#FFEBEE',
          padding: '10px',
          borderRadius: '6px',
          border: '1px solid #FFCDD2',
          marginBottom: '15px'
        }}>
          <p style={{
            fontSize: '12px',
            color: '#C62828',
            margin: 0,
            textAlign: 'center'
          }}>
            ⚠️ Includes {readingTask.missedDaysCount} missed day{readingTask.missedDaysCount > 1 ? 's' : ''}
          </p>
        </div>
      )}

    </div>
  );
}

export default ReadingTaskDisplay;
