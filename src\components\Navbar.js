import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaBars, FaTimes, FaSearch, FaUser, FaComments, FaBell, FaDatabase, FaQuestionCircle, FaSignOutAlt } from 'react-icons/fa';
import UserProfileImage from './UserProfileImage';
import { initializeUserData, getStoredUserData, logoutUser } from '../utils/userApi';
import { useNavigate } from 'react-router-dom';

function UserSlider({ user, onUserUpdate, onLogout }) {
  const menu = [
    {
      icon: <FaUser size={20} />,
      title: 'Account',
      subtitle: 'Privacy, security, change email or number',
    },
    {
      icon: <FaComments size={20} />,
      title: 'Chats',
      subtitle: 'Theme, wallpapers, chat history',
    },
    {
      icon: <FaBell size={20} />,
      title: 'Notifications',
      subtitle: 'Message, group & call tones',
    },
    {
      icon: <FaDatabase size={20} />,
      title: 'Storage and data',
      subtitle: 'Network usage, auto-download',
    },
    {
      icon: <FaQuestionCircle size={20} />,
      title: 'Help',
      subtitle: 'Help centre, contact us, privacy policy',
    },
  ];

  return (
    <div style={{
      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
      height: '100vh',
      color: '#000',
      padding: '20px',
      width: '100%',
      maxWidth: 380,
      display: 'flex',
      flexDirection: 'column',
      overflowY: 'auto'
    }}>
      {/* Profile Section */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
        paddingTop: '20px'
      }}>
        {/* Profile Image */}
        <UserProfileImage
          user={user}
          onImageUpdate={onUserUpdate}
          size={80}
          showUploadButton={true}
          style={{ marginBottom: 16 }}
        />
        {/* User Name */}
        <h2 style={{
          fontWeight: '700',
          fontSize: '22px',
          marginBottom: '8px',
          textAlign: 'center',
          color: '#333'
        }}>
          {user?.userName || 'User'}
        </h2>
        {/* User Email */}
        <div style={{
          color: '#666',
          fontSize: '14px',
          marginBottom: '0',
          textAlign: 'center',
          fontWeight: '500'
        }}>
          {user?.email || '<EMAIL>'}
        </div>
      </div>
      {/* Menu Items */}
      <div style={{
        width: '100%',
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}>
        <div>
          {menu.map((item, idx) => (
            <div
              key={idx}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                marginBottom: '6px',
                cursor: 'pointer',
                padding: '12px 16px',
                borderRadius: '12px',
                transition: 'all 0.3s ease',
                background: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))';
                e.target.style.transform = 'translateX(5px)';
                e.target.style.boxShadow = '0 4px 15px rgba(212, 175, 55, 0.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'transparent';
                e.target.style.transform = 'translateX(0px)';
                e.target.style.boxShadow = 'none';
              }}
            >
              <div style={{
                color: '#d4af37',
                transition: 'all 0.3s ease',
                fontSize: '18px'
              }}>
                {item.icon}
              </div>
              <div>
                <div style={{
                  fontWeight: '600',
                  fontSize: '15px',
                  color: '#333',
                  marginBottom: '2px'
                }}>
                  {item.title}
                </div>
                <div style={{
                  fontSize: '11px',
                  color: '#666',
                  lineHeight: '1.3'
                }}>
                  {item.subtitle}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Logout Button */}
        <div style={{ marginTop: '20px', paddingBottom: '20px' }}>
          <button
            onClick={onLogout}
            style={{
              width: '100%',
              padding: '12px 20px',
              borderRadius: '12px',
              border: 'none',
              background: 'linear-gradient(135deg, #dc3545, #e74c3c)',
              color: 'white',
              fontSize: '15px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              boxShadow: '0 4px 15px rgba(220, 53, 69, 0.2)'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 6px 25px rgba(220, 53, 69, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0px)';
              e.target.style.boxShadow = '0 4px 15px rgba(220, 53, 69, 0.2)';
            }}
          >
            <FaSignOutAlt size={14} />
            Logout
          </button>
        </div>
      </div>
    </div>
  );
}

function Navbar({ onLogout }) {
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userData, setUserData] = useState(null);

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const data = await initializeUserData();
        setUserData(data);
      } catch (error) {
        console.error('Error loading user data in navbar:', error);
        const storedData = getStoredUserData();
        setUserData(storedData);
      }
    };

    loadUserData();
  }, []);

  const handleUserUpdate = (updatedUser) => {
    setUserData(updatedUser);
  };

  const handleLogout = () => {
    logoutUser();
    if (onLogout) {
      onLogout();
    }
    navigate('/login');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };



  return (
    <>
      {/* Sidebar Backdrop */}
      {sidebarOpen && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            zIndex: 999,
            transition: "opacity 0.3s ease",
            backdropFilter: "blur(5px)"
          }}
          onClick={closeSidebar}
        />
      )}

      {/* Sidebar */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: sidebarOpen ? 0 : "-380px",
          width: "380px",
          height: "100%",
          transition: "left 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
          zIndex: 1000,
          background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
          boxShadow: sidebarOpen ? "10px 0px 30px rgba(0,0,0,0.15)" : "none",
          borderRight: "1px solid rgba(212, 175, 55, 0.1)"
        }}
      >
        <div style={{
          position: "absolute",
          right: "20px",
          top: "20px",
          zIndex: 2
        }}>
          <div
            onClick={closeSidebar}
            style={{
              width: "40px",
              height: "40px",
              borderRadius: "50%",
              background: "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))",
              border: "1px solid rgba(212, 175, 55, 0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              transition: "all 0.3s ease"
            }}
            onMouseEnter={(e) => {
              e.target.style.background = "linear-gradient(135deg, #d4af37, #f9e79f)";
              e.target.style.transform = "scale(1.1) rotate(90deg)";
            }}
            onMouseLeave={(e) => {
              e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))";
              e.target.style.transform = "scale(1) rotate(0deg)";
            }}
          >
            <FaTimes
              size={16}
              color="#d4af37"
              style={{
                transition: "all 0.3s ease"
              }}
            />
          </div>
        </div>
        <UserSlider user={userData} onUserUpdate={handleUserUpdate} onLogout={handleLogout} />
      </div>

      {/* Navbar */}
      <nav
        style={{
          background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
          height: "70px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "0 30px",
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 999,
          boxShadow: "0 2px 20px rgba(0,0,0,0.08)",
          backdropFilter: "blur(10px)",
          borderBottom: "1px solid rgba(212, 175, 55, 0.1)",
          transition: "all 0.3s ease"
        }}
      >
        {/* Left: Settings */}
        <div style={{ flexShrink: 0 }}>
          <div
            onClick={toggleSidebar}
            style={{
              width: "45px",
              height: "45px",
              borderRadius: "12px",
              background: "linear-gradient(135deg, #d4af37, #f9e79f)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              transition: "all 0.3s ease",
              boxShadow: "0 4px 15px rgba(212, 175, 55, 0.2)",
              transform: sidebarOpen ? "rotate(90deg)" : "rotate(0deg)"
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = sidebarOpen ? "rotate(90deg) scale(1.1)" : "rotate(0deg) scale(1.1)";
              e.target.style.boxShadow = "0 6px 25px rgba(212, 175, 55, 0.4)";
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = sidebarOpen ? "rotate(90deg) scale(1)" : "rotate(0deg) scale(1)";
              e.target.style.boxShadow = "0 4px 15px rgba(212, 175, 55, 0.2)";
            }}
          >
            <FaBars
              size={18}
              color="white"
              style={{
                transition: "all 0.3s ease"
              }}
            />
          </div>
        </div>

        {/* Center: Home, Surahs, PageName, Tafser, Hadith */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexGrow: 1,
            gap: "35px",
          }}
        >
          {[
            { to: "/home", label: "Home" },
            { to: "/surahs", label: "Surahs" },
            { to: "/tasmee", label: "Tasmee" },
            { to: "/tafser", label: "Tafser" },
            { to: "/hadith", label: "Hadith" }
          ].map((link, index) => (
            <Link
              key={index}
              to={link.to}
              style={{
                color: location.pathname === link.to ? "#d4af37" : "#8B7355",
                textDecoration: "none",
                fontWeight: "600",
                fontSize: "16px",
                padding: "8px 16px",
                borderRadius: "25px",
                transition: "all 0.3s ease",
                position: "relative",
                background: location.pathname === link.to
                  ? "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))"
                  : "transparent",
                border: location.pathname === link.to
                  ? "1px solid rgba(212, 175, 55, 0.2)"
                  : "1px solid transparent"
              }}
              onMouseEnter={(e) => {
                if (location.pathname !== link.to) {
                  e.target.style.color = "#d4af37";
                  e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.05), rgba(249, 231, 159, 0.05))";
                  e.target.style.transform = "translateY(-2px)";
                }
              }}
              onMouseLeave={(e) => {
                if (location.pathname !== link.to) {
                  e.target.style.color = "#8B7355";
                  e.target.style.background = "transparent";
                  e.target.style.transform = "translateY(0px)";
                }
              }}
            >
              {link.label}
            </Link>
          ))}
        </div>

        {/* Right: Search */}
        <div style={{ flexShrink: 0 }}>
          <div
            style={{
              width: "45px",
              height: "45px",
              borderRadius: "12px",
              background: "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))",
              border: "1px solid rgba(212, 175, 55, 0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              transition: "all 0.3s ease",
              backdropFilter: "blur(10px)"
            }}
            onMouseEnter={(e) => {
              e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(249, 231, 159, 0.2))";
              e.target.style.transform = "scale(1.05)";
              e.target.style.boxShadow = "0 4px 15px rgba(212, 175, 55, 0.2)";
            }}
            onMouseLeave={(e) => {
              e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))";
              e.target.style.transform = "scale(1)";
              e.target.style.boxShadow = "none";
            }}
          >
            <FaSearch
              size={18}
              color="#d4af37"
              style={{
                transition: "all 0.3s ease"
              }}
            />
          </div>
        </div>
      </nav>
    </>
  );
}

export default Navbar;
