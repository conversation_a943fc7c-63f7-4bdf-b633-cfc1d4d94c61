import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaBars, FaTimes, FaSearch, FaUser, FaComments, FaBell, FaDatabase, FaQuestionCircle } from 'react-icons/fa';
import userDefault from '../assets/user.png'; // adjust path as needed

function UserSlider({ user }) {
  const menu = [
    {
      icon: <FaUser size={20} />,
      title: 'Account',
      subtitle: 'Privacy, security, change email or number',
    },
    {
      icon: <FaComments size={20} />,
      title: 'Chats',
      subtitle: 'Theme, wallpapers, chat history',
    },
    {
      icon: <FaBell size={20} />,
      title: 'Notifications',
      subtitle: 'Message, group & call tones',
    },
    {
      icon: <FaDatabase size={20} />,
      title: 'Storage and data',
      subtitle: 'Network usage, auto-download',
    },
    {
      icon: <FaQuestionCircle size={20} />,
      title: 'Help',
      subtitle: 'Help centre, contact us, privacy policy',
    },
  ];

  return (
    <div style={{
      background: '#f5f5f5',
      minHeight: '100vh',
      color: '#000',
      padding: '32px 0',
      width: '100%',
      maxWidth: 350,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center'
    }}>
      {/* Profile Image */}
      <img
        src={user?.image ? user.image : userDefault}
        alt="User"
        style={{
          width: 80,
          height: 80,
          borderRadius: '50%',
          objectFit: 'cover',
          marginBottom: 16,
          border: '3px solid #fff'
        }}
        onError={e => { e.target.onerror = null; e.target.src = userDefault; }}
      />
      {/* User Name */}
      <h2 style={{
        fontWeight: 'bold',
        fontSize: 22,
        marginBottom: 8,
        textAlign: 'center'
      }}>
        {user?.userName || 'User'}
      </h2>
      {/* User Email */}
      <div style={{
        color: '#555',
        fontSize: 13,
        marginBottom: 28,
        textAlign: 'center'
      }}>
        {user?.email || '<EMAIL>'}
      </div>
      {/* Menu Items */}
      <div style={{ width: '100%', padding: '0 20px' }}>
        {menu.map((item, idx) => (
          <div key={idx} style={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: 12,
            marginBottom: 22,
            cursor: 'pointer'
          }}>
            <div style={{ marginTop: 2 }}>{item.icon}</div>
            <div>
              <div style={{ fontWeight: 'bold', fontSize: 14 }}>{item.title}</div>
              <div style={{ fontSize: 11, color: '#888', marginTop: 2 }}>{item.subtitle}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function Navbar() {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Get user data from localStorage
  const userData = JSON.parse(localStorage.getItem('userData') || '{}');

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  const getPageTitle = () => {
    switch (location.pathname) {
      case "/home":
        return "Home";
      case "/surahs":
        return "Surahs";
      case "/surahayat":
        return "Surah Ayat";
      case "/tafser":
        return "Tafser";
      case "/hadith":
        return "Hadith";
      default:
        return "Quran Website";
    }
  };

  return (
    <>
      {/* Sidebar */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: sidebarOpen ? 0 : "-350px",
          width: "350px",
          height: "100%",
          transition: "left 0.3s ease",
          zIndex: 1000,
          boxShadow: sidebarOpen ? "5px 0px 20px rgba(0,0,0,0.18), 0 0 1px #aaa" : "none",
        }}
      >
        <div style={{ textAlign: "right", margin: "20px 20px 0 0", position: "absolute", right: 0, top: 0, zIndex: 2 }}>
          <FaTimes
            size={24}
            color="#d4af37"
            style={{ cursor: "pointer" }}
            onClick={closeSidebar}
          />
        </div>
        <UserSlider user={userData} />
      </div>

      {/* Navbar */}
      <nav
        style={{
          backgroundColor: "#f5f5f5",
          height: "60px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "0 30px",
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 999,
        }}
      >
        {/* Left: Settings */}
        <div style={{ flexShrink: 0 }}>
          <FaBars
            size={22}
            color="#bfa131"
            style={{ cursor: "pointer" }}
            onClick={toggleSidebar}
          />
        </div>

        {/* Center: Home, Surahs, PageName, Tafser, Hadith */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexGrow: 1,
            gap: "40px",
          }}
        >
          <Link to="/home" style={{ color: "#bfa131", textDecoration: "none", fontWeight: "bold" }}>
            Home
          </Link>

          <Link to="/surahs" style={{ color: "#bfa131", textDecoration: "none", fontWeight: "bold" }}>
            Surahs
          </Link>

          <div style={{ color: "#bfa131", fontWeight: "bold", fontSize: "20px" }}>
            {getPageTitle()}
          </div>

          <Link to="/tafser" style={{ color: "#bfa131", textDecoration: "none", fontWeight: "bold" }}>
            Tafser
          </Link>

          <Link to="/hadith" style={{ color: "#bfa131", textDecoration: "none", fontWeight: "bold" }}>
            Hadith
          </Link>
        </div>

        {/* Right: Search */}
        <div style={{ flexShrink: 0 }}>
          <FaSearch
            size={20}
            color="#bfa131"
            style={{ cursor: "pointer" }}
          />
        </div>
      </nav>
    </>
  );
}

export default Navbar;
