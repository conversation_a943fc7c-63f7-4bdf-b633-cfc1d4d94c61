import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaBars, FaTimes, FaSearch, FaUser, FaBell, FaQuestionCircle, FaSignOutAlt, FaUsers, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import UserProfileImage from './UserProfileImage';
import { initializeUserData, getStoredUserData, logoutUser, updateStoredUserData } from '../utils/userApi';
import { useNavigate } from 'react-router-dom';

function UserSlider({ user, onUserUpdate, onLogout }) {
  const [expandedSection, setExpandedSection] = useState(null);
  const [topUsers, setTopUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [editUsername, setEditUsername] = useState(user?.userName || '');
  const [newPassword, setNewPassword] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Update editUsername when user prop changes
  useEffect(() => {
    setEditUsername(user?.userName || '');
  }, [user]);

  const menu = [
    {
      icon: <FaUser size={20} />,
      title: 'Account',
      subtitle: 'Edit Profile, Privacy, Security',
      id: 'account'
    },
    {
      icon: <FaBell size={20} />,
      title: 'Notifications',
      subtitle: 'Message, group & call tones',
      id: 'notifications'
    },
    {
      icon: <FaUsers size={20} />,
      title: 'Top 10 Users',
      subtitle: 'Latest Registered Users',
      id: 'topUsers'
    },
    {
      icon: <FaQuestionCircle size={20} />,
      title: 'Help',
      subtitle: 'Help centre, contact us, privacy policy',
      id: 'help'
    },
  ];

  // Fetch top 10 users
  const fetchTopUsers = async () => {
    setLoadingUsers(true);
    try {
      const response = await fetch('https://kotaby.duckdns.org/users');
      if (response.ok) {
        const users = await response.json();
        // Sort by ID (assuming higher ID means newer) and take top 10
        const sortedUsers = users.sort((a, b) => b.id - a.id).slice(0, 10);
        setTopUsers(sortedUsers);
      } else {
        console.error('Failed to fetch users');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Update user profile
  const handleUpdateProfile = async () => {
    if (!user?.id) return;

    // Validate password if provided
    if (newPassword && newPassword.length < 6) {
      alert('Password must be at least 6 characters long');
      return;
    }

    setIsUpdating(true);
    try {
      const updateData = {
        userName: editUsername,
        email: user.email, // Keep existing email
        ...(newPassword && { password: newPassword }) // Only include password if provided
      };

      const response = await fetch(`https://kotaby.duckdns.org/users/${user.id}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        // Update stored user data
        updateStoredUserData(updatedUser);
        // Notify parent component
        onUserUpdate(updatedUser);
        // Clear password field
        setNewPassword('');
        alert('Profile updated successfully!');
      } else {
        const errorData = await response.json();
        alert('Failed to update profile: ' + (errorData.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle menu item click
  const handleMenuClick = (itemId) => {
    if (itemId === 'topUsers') {
      if (expandedSection === 'topUsers') {
        setExpandedSection(null);
      } else {
        setExpandedSection('topUsers');
        fetchTopUsers();
      }
    } else if (itemId === 'account') {
      if (expandedSection === 'account') {
        setExpandedSection(null);
      } else {
        setExpandedSection('account');
      }
    } else {
      // For other sections, just toggle
      setExpandedSection(expandedSection === itemId ? null : itemId);
    }
  };

  return (
    <div style={{
      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
      height: '100vh',
      color: '#000',
      padding: '20px',
      width: '100%',
      maxWidth: 380,
      display: 'flex',
      flexDirection: 'column',
      overflowY: 'auto'
    }}>
      {/* Profile Section */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
        paddingTop: '20px'
      }}>
        {/* Profile Image */}
        <UserProfileImage
          user={user}
          onImageUpdate={onUserUpdate}
          size={80}
          showUploadButton={true}
          style={{ marginBottom: 16 }}
        />
        {/* User Name */}
        <h2 style={{
          fontWeight: '700',
          fontSize: '22px',
          marginBottom: '8px',
          textAlign: 'center',
          color: '#333'
        }}>
          {user?.userName || 'User'}
        </h2>
        {/* User Email */}
        <div style={{
          color: '#666',
          fontSize: '14px',
          marginBottom: '0',
          textAlign: 'center',
          fontWeight: '500'
        }}>
          {user?.email || '<EMAIL>'}
        </div>
      </div>
      {/* Menu Items */}
      <div style={{
        width: '100%',
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}>
        <div>
          {menu.map((item, idx) => (
            <div key={idx}>
              <div
                onClick={() => handleMenuClick(item.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '6px',
                  cursor: 'pointer',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  transition: 'all 0.3s ease',
                  background: expandedSection === item.id
                    ? 'linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(249, 231, 159, 0.15))'
                    : 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (expandedSection !== item.id) {
                    e.target.style.background = 'linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))';
                  }
                  e.target.style.transform = 'translateX(5px)';
                  e.target.style.boxShadow = '0 4px 15px rgba(212, 175, 55, 0.1)';
                }}
                onMouseLeave={(e) => {
                  if (expandedSection !== item.id) {
                    e.target.style.background = 'transparent';
                  } else {
                    e.target.style.background = 'linear-gradient(135deg, rgba(212, 175, 55, 0.15), rgba(249, 231, 159, 0.15))';
                  }
                  e.target.style.transform = 'translateX(0px)';
                  e.target.style.boxShadow = 'none';
                }}
              >
                <div style={{
                  color: '#d4af37',
                  transition: 'all 0.3s ease',
                  fontSize: '18px'
                }}>
                  {item.icon}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{
                    fontWeight: '600',
                    fontSize: '15px',
                    color: '#333',
                    marginBottom: '2px'
                  }}>
                    {item.title}
                  </div>
                  <div style={{
                    fontSize: '11px',
                    color: '#666',
                    lineHeight: '1.3'
                  }}>
                    {item.subtitle}
                  </div>
                </div>
                {(item.id === 'account' || item.id === 'topUsers') && (
                  <div style={{
                    color: '#d4af37',
                    fontSize: '12px'
                  }}>
                    {expandedSection === item.id ? <FaChevronUp /> : <FaChevronDown />}
                  </div>
                )}
              </div>

              {/* Expanded Content */}
              {expandedSection === item.id && (
                <div style={{
                  marginLeft: '20px',
                  marginRight: '10px',
                  marginBottom: '15px',
                  padding: '15px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '10px',
                  border: '1px solid rgba(212, 175, 55, 0.1)'
                }}>
                  {item.id === 'account' && (
                    <div>
                      {/* Username Field */}
                      <div style={{ marginBottom: '15px' }}>
                        <label style={{
                          display: 'block',
                          fontSize: '12px',
                          fontWeight: '600',
                          color: '#666',
                          marginBottom: '5px'
                        }}>
                          Username
                        </label>
                        <input
                          type="text"
                          value={editUsername}
                          onChange={(e) => setEditUsername(e.target.value)}
                          style={{
                            width: '100%',
                            padding: '8px 12px',
                            borderRadius: '8px',
                            border: '1px solid #ddd',
                            fontSize: '14px',
                            outline: 'none',
                            transition: 'border-color 0.3s ease'
                          }}
                          onFocus={(e) => e.target.style.borderColor = '#d4af37'}
                          onBlur={(e) => e.target.style.borderColor = '#ddd'}
                        />
                      </div>

                      {/* Email Field (Read-only) */}
                      <div style={{ marginBottom: '15px' }}>
                        <label style={{
                          display: 'block',
                          fontSize: '12px',
                          fontWeight: '600',
                          color: '#666',
                          marginBottom: '5px'
                        }}>
                          Email
                        </label>
                        <div style={{
                          width: '100%',
                          padding: '8px 12px',
                          borderRadius: '8px',
                          backgroundColor: '#e9ecef',
                          border: '1px solid #ddd',
                          fontSize: '14px',
                          color: '#666'
                        }}>
                          {user?.email || 'No email'}
                        </div>
                      </div>

                      {/* Password Field */}
                      <div style={{ marginBottom: '15px' }}>
                        <label style={{
                          display: 'block',
                          fontSize: '12px',
                          fontWeight: '600',
                          color: '#666',
                          marginBottom: '5px'
                        }}>
                          New Password (optional)
                        </label>
                        <input
                          type="password"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          placeholder="Enter new password"
                          style={{
                            width: '100%',
                            padding: '8px 12px',
                            borderRadius: '8px',
                            border: '1px solid #ddd',
                            fontSize: '14px',
                            outline: 'none',
                            transition: 'border-color 0.3s ease'
                          }}
                          onFocus={(e) => e.target.style.borderColor = '#d4af37'}
                          onBlur={(e) => e.target.style.borderColor = '#ddd'}
                        />
                      </div>

                      {/* Update Button */}
                      <button
                        onClick={handleUpdateProfile}
                        disabled={isUpdating}
                        style={{
                          width: '100%',
                          padding: '10px',
                          borderRadius: '8px',
                          border: 'none',
                          background: isUpdating
                            ? '#ccc'
                            : 'linear-gradient(135deg, #d4af37, #f9e79f)',
                          color: 'white',
                          fontSize: '14px',
                          fontWeight: '600',
                          cursor: isUpdating ? 'not-allowed' : 'pointer',
                          transition: 'all 0.3s ease'
                        }}
                      >
                        {isUpdating ? 'Updating...' : 'Update Profile'}
                      </button>
                    </div>
                  )}

                  {item.id === 'topUsers' && (
                    <div>
                      <h4 style={{
                        fontSize: '14px',
                        fontWeight: '600',
                        color: '#333',
                        marginBottom: '10px'
                      }}>
                        Latest Registered Users
                      </h4>
                      {loadingUsers ? (
                        <div style={{ textAlign: 'center', color: '#666', fontSize: '12px' }}>
                          Loading users...
                        </div>
                      ) : (
                        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                          {topUsers.map((topUser, index) => (
                            <div
                              key={topUser.id}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                padding: '8px',
                                marginBottom: '5px',
                                backgroundColor: 'white',
                                borderRadius: '6px',
                                border: '1px solid #eee'
                              }}
                            >
                              <div style={{
                                width: '24px',
                                height: '24px',
                                borderRadius: '50%',
                                backgroundColor: '#d4af37',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginRight: '10px',
                                fontSize: '10px',
                                fontWeight: 'bold',
                                color: 'white'
                              }}>
                                {index + 1}
                              </div>
                              <div style={{ flex: 1 }}>
                                <div style={{
                                  fontSize: '12px',
                                  fontWeight: '600',
                                  color: '#333'
                                }}>
                                  {topUser.userName || 'Unknown User'}
                                </div>
                                <div style={{
                                  fontSize: '10px',
                                  color: '#666'
                                }}>
                                  ID: {topUser.id}
                                </div>
                              </div>
                            </div>
                          ))}
                          {topUsers.length === 0 && !loadingUsers && (
                            <div style={{ textAlign: 'center', color: '#666', fontSize: '12px' }}>
                              No users found
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Logout Button */}
        <div style={{ marginTop: '20px', paddingBottom: '20px' }}>
          <button
            onClick={onLogout}
            style={{
              width: '100%',
              padding: '12px 20px',
              borderRadius: '12px',
              border: 'none',
              background: 'linear-gradient(135deg, #dc3545, #e74c3c)',
              color: 'white',
              fontSize: '15px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              boxShadow: '0 4px 15px rgba(220, 53, 69, 0.2)'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 6px 25px rgba(220, 53, 69, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0px)';
              e.target.style.boxShadow = '0 4px 15px rgba(220, 53, 69, 0.2)';
            }}
          >
            <FaSignOutAlt size={14} />
            Logout
          </button>
        </div>
      </div>
    </div>
  );
}

function Navbar({ onLogout }) {
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userData, setUserData] = useState(null);

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const data = await initializeUserData();
        setUserData(data);
      } catch (error) {
        console.error('Error loading user data in navbar:', error);
        const storedData = getStoredUserData();
        setUserData(storedData);
      }
    };

    loadUserData();
  }, []);

  const handleUserUpdate = (updatedUser) => {
    setUserData(updatedUser);
  };

  const handleLogout = () => {
    logoutUser();
    if (onLogout) {
      onLogout();
    }
    navigate('/login');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };



  return (
    <>
      {/* Sidebar Backdrop */}
      {sidebarOpen && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            zIndex: 999,
            transition: "opacity 0.3s ease",
            backdropFilter: "blur(5px)"
          }}
          onClick={closeSidebar}
        />
      )}

      {/* Sidebar */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: sidebarOpen ? 0 : "-380px",
          width: "380px",
          height: "100%",
          transition: "left 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
          zIndex: 1000,
          background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
          boxShadow: sidebarOpen ? "10px 0px 30px rgba(0,0,0,0.15)" : "none",
          borderRight: "1px solid rgba(212, 175, 55, 0.1)"
        }}
      >
        <div style={{
          position: "absolute",
          right: "20px",
          top: "20px",
          zIndex: 2
        }}>
          <div
            onClick={closeSidebar}
            style={{
              width: "40px",
              height: "40px",
              borderRadius: "50%",
              background: "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))",
              border: "1px solid rgba(212, 175, 55, 0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              transition: "all 0.3s ease"
            }}
            onMouseEnter={(e) => {
              e.target.style.background = "linear-gradient(135deg, #d4af37, #f9e79f)";
              e.target.style.transform = "scale(1.1) rotate(90deg)";
            }}
            onMouseLeave={(e) => {
              e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))";
              e.target.style.transform = "scale(1) rotate(0deg)";
            }}
          >
            <FaTimes
              size={16}
              color="#d4af37"
              style={{
                transition: "all 0.3s ease"
              }}
            />
          </div>
        </div>
        <UserSlider user={userData} onUserUpdate={handleUserUpdate} onLogout={handleLogout} />
      </div>

      {/* Navbar */}
      <nav
        style={{
          background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
          height: "70px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "0 30px",
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 999,
          boxShadow: "0 2px 20px rgba(0,0,0,0.08)",
          backdropFilter: "blur(10px)",
          borderBottom: "1px solid rgba(212, 175, 55, 0.1)",
          transition: "all 0.3s ease"
        }}
      >
        {/* Left: Settings */}
        <div style={{ flexShrink: 0 }}>
          <div
            onClick={toggleSidebar}
            style={{
              width: "45px",
              height: "45px",
              borderRadius: "12px",
              background: "linear-gradient(135deg, #d4af37, #f9e79f)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              transition: "all 0.3s ease",
              boxShadow: "0 4px 15px rgba(212, 175, 55, 0.2)",
              transform: sidebarOpen ? "rotate(90deg)" : "rotate(0deg)"
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = sidebarOpen ? "rotate(90deg) scale(1.1)" : "rotate(0deg) scale(1.1)";
              e.target.style.boxShadow = "0 6px 25px rgba(212, 175, 55, 0.4)";
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = sidebarOpen ? "rotate(90deg) scale(1)" : "rotate(0deg) scale(1)";
              e.target.style.boxShadow = "0 4px 15px rgba(212, 175, 55, 0.2)";
            }}
          >
            <FaBars
              size={18}
              color="white"
              style={{
                transition: "all 0.3s ease"
              }}
            />
          </div>
        </div>

        {/* Center: Home, Surahs, PageName, Tafser, Hadith */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexGrow: 1,
            gap: "35px",
          }}
        >
          {[
            { to: "/home", label: "Home" },
            { to: "/surahs", label: "Surahs" },
            { to: "/tasmee", label: "Tasmee" },
            { to: "/tafser", label: "Tafser" },
            { to: "/hadith", label: "Hadith" }
          ].map((link, index) => (
            <Link
              key={index}
              to={link.to}
              style={{
                color: location.pathname === link.to ? "#d4af37" : "#8B7355",
                textDecoration: "none",
                fontWeight: "600",
                fontSize: "16px",
                padding: "8px 16px",
                borderRadius: "25px",
                transition: "all 0.3s ease",
                position: "relative",
                background: location.pathname === link.to
                  ? "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))"
                  : "transparent",
                border: location.pathname === link.to
                  ? "1px solid rgba(212, 175, 55, 0.2)"
                  : "1px solid transparent"
              }}
              onMouseEnter={(e) => {
                if (location.pathname !== link.to) {
                  e.target.style.color = "#d4af37";
                  e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.05), rgba(249, 231, 159, 0.05))";
                  e.target.style.transform = "translateY(-2px)";
                }
              }}
              onMouseLeave={(e) => {
                if (location.pathname !== link.to) {
                  e.target.style.color = "#8B7355";
                  e.target.style.background = "transparent";
                  e.target.style.transform = "translateY(0px)";
                }
              }}
            >
              {link.label}
            </Link>
          ))}
        </div>

        {/* Right: Search */}
        <div style={{ flexShrink: 0 }}>
          <div
            style={{
              width: "45px",
              height: "45px",
              borderRadius: "12px",
              background: "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))",
              border: "1px solid rgba(212, 175, 55, 0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              transition: "all 0.3s ease",
              backdropFilter: "blur(10px)"
            }}
            onMouseEnter={(e) => {
              e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(249, 231, 159, 0.2))";
              e.target.style.transform = "scale(1.05)";
              e.target.style.boxShadow = "0 4px 15px rgba(212, 175, 55, 0.2)";
            }}
            onMouseLeave={(e) => {
              e.target.style.background = "linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(249, 231, 159, 0.1))";
              e.target.style.transform = "scale(1)";
              e.target.style.boxShadow = "none";
            }}
          >
            <FaSearch
              size={18}
              color="#d4af37"
              style={{
                transition: "all 0.3s ease"
              }}
            />
          </div>
        </div>
      </nav>
    </>
  );
}

export default Navbar;
