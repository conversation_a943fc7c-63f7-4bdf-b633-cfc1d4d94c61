// User API utilities for fetching user data and handling image uploads

const BASE_URL = 'https://kotaby.duckdns.org';

// Get user data from localStorage
export const getStoredUserData = () => {
  try {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing stored user data:', error);
    return null;
  }
};

// Fetch user data from API by ID
export const fetchUserById = async (userId) => {
  try {
    const response = await fetch(`${BASE_URL}/users/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user data: ${response.status}`);
    }

    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

// Upload user profile image
export const uploadUserImage = async (userId, imageFile) => {
  try {
    const formData = new FormData();
    formData.append('file', imageFile);

    const response = await fetch(`${BASE_URL}/users/${userId}/photo`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Failed to upload image: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

// Get user profile image URL
export const getUserImageUrl = (userId, imagePath) => {
  if (!imagePath) return null;
  
  // If imagePath is already a full URL, return it
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  
  // Otherwise, construct the full URL
  return `${BASE_URL}/users/${userId}/photo/${imagePath}`;
};

// Update user data in localStorage
export const updateStoredUserData = (userData) => {
  try {
    localStorage.setItem('user', JSON.stringify(userData));
    // Also update userData key for backward compatibility
    localStorage.setItem('userData', JSON.stringify(userData));
    return true;
  } catch (error) {
    console.error('Error updating stored user data:', error);
    return false;
  }
};

// Initialize user data on app load
export const initializeUserData = async () => {
  try {
    const storedUser = getStoredUserData();
    if (!storedUser || !storedUser.id) {
      console.warn('No valid user data found in localStorage');
      return null;
    }

    // Fetch fresh user data from API
    const freshUserData = await fetchUserById(storedUser.id);
    
    // Update localStorage with fresh data
    updateStoredUserData(freshUserData);
    
    return freshUserData;
  } catch (error) {
    console.error('Error initializing user data:', error);
    // Return stored data as fallback
    return getStoredUserData();
  }
};

// Check if user is authenticated
export const isUserAuthenticated = () => {
  try {
    const userData = getStoredUserData();
    // Check if userData exists and has required fields
    const isValid = userData &&
                   userData.id &&
                   userData.email &&
                   userData.userName;

    console.log('Authentication check:', { userData, isValid });
    return isValid;
  } catch (error) {
    console.error('Error in isUserAuthenticated:', error);
    return false;
  }
};

// Logout user
export const logoutUser = () => {
  try {
    console.log('Clearing all user data from localStorage...');
    localStorage.removeItem('user');
    localStorage.removeItem('userData');
    // Clear any other potential auth-related data
    localStorage.clear();
    console.log('User data cleared successfully');
    return true;
  } catch (error) {
    console.error('Error during logout:', error);
    return false;
  }
};

// Force clear all authentication data (for debugging)
export const forceLogout = () => {
  console.log('Force clearing all localStorage data...');
  localStorage.clear();
  window.location.reload();
};

// Create reading plan on backend server
export const createReadingPlanOnServer = async (userId, days) => {
  try {
    const response = await fetch(`http://localhost:9090/tasks/create-reading-plan?userId=${userId}&days=${days}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to create reading plan on server: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plan created on server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error creating reading plan on server:', error);
    throw error;
  }
};

// Update daily reading progress
export const updateReadingProgress = async (userId, progress) => {
  try {
    const response = await fetch(`http://localhost:9090/tasks/${userId}/progress?progress=${progress}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to update reading progress: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading progress updated on server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error updating reading progress:', error);
    throw error;
  }
};

// Mark reading plan as complete
export const completeReadingPlan = async (userId) => {
  try {
    const response = await fetch(`http://localhost:9090/tasks/${userId}/complete`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to complete reading plan: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plan completed on server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error completing reading plan:', error);
    throw error;
  }
};

// Get user's active reading plan
export const getUserReadingPlan = async (userId) => {
  try {
    const response = await fetch(`http://localhost:9090/tasks/user/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get user reading plan: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ User reading plan fetched from server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error fetching user reading plan:', error);
    throw error;
  }
};

// Delete/cancel user's reading plan
export const deleteUserReadingPlan = async (userId) => {
  try {
    const response = await fetch(`http://localhost:9090/tasks/user/${userId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete reading plan: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plan deleted from server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error deleting reading plan:', error);
    throw error;
  }
};

// Get user's completed reading plans summary
export const getUserReadingPlansSummary = async (userId) => {
  try {
    const response = await fetch(`http://localhost:9090/tasks/user/${userId}/summary`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get reading plans summary: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plans summary fetched from server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error fetching reading plans summary:', error);
    throw error;
  }
};
