// User API utilities for fetching user data and handling image uploads

const BASE_URL = 'https://kotaby.duckdns.org';
const TASKS_BASE_URL = 'http://localhost:9090';

// Get user data from localStorage
export const getStoredUserData = () => {
  try {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing stored user data:', error);
    return null;
  }
};

// Fetch user data from API by ID
export const fetchUserById = async (userId) => {
  try {
    const response = await fetch(`${BASE_URL}/users/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user data: ${response.status}`);
    }

    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

// Upload user profile image
export const uploadUserImage = async (userId, imageFile) => {
  try {
    const formData = new FormData();
    formData.append('file', imageFile);

    const response = await fetch(`${BASE_URL}/users/${userId}/photo`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Failed to upload image: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

// Get user profile image URL
export const getUserImageUrl = (userId, imagePath) => {
  if (!imagePath) return null;
  
  // If imagePath is already a full URL, return it
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  
  // Otherwise, construct the full URL
  return `${BASE_URL}/users/${userId}/photo/${imagePath}`;
};

// Update user data in localStorage
export const updateStoredUserData = (userData) => {
  try {
    localStorage.setItem('user', JSON.stringify(userData));
    // Also update userData key for backward compatibility
    localStorage.setItem('userData', JSON.stringify(userData));
    return true;
  } catch (error) {
    console.error('Error updating stored user data:', error);
    return false;
  }
};

// Initialize user data on app load
export const initializeUserData = async () => {
  try {
    const storedUser = getStoredUserData();
    if (!storedUser || !storedUser.id) {
      console.warn('No valid user data found in localStorage');
      return null;
    }

    // Fetch fresh user data from API
    const freshUserData = await fetchUserById(storedUser.id);
    
    // Update localStorage with fresh data
    updateStoredUserData(freshUserData);
    
    return freshUserData;
  } catch (error) {
    console.error('Error initializing user data:', error);
    // Return stored data as fallback
    return getStoredUserData();
  }
};

// Check if user is authenticated
export const isUserAuthenticated = () => {
  try {
    const userData = getStoredUserData();
    // Check if userData exists and has required fields
    const isValid = userData &&
                   userData.id &&
                   userData.email &&
                   userData.userName;

    console.log('Authentication check:', { userData, isValid });
    return isValid;
  } catch (error) {
    console.error('Error in isUserAuthenticated:', error);
    return false;
  }
};

// Logout user
export const logoutUser = () => {
  try {
    console.log('Clearing all user data from localStorage...');
    localStorage.removeItem('user');
    localStorage.removeItem('userData');
    // Clear any other potential auth-related data
    localStorage.clear();
    console.log('User data cleared successfully');
    return true;
  } catch (error) {
    console.error('Error during logout:', error);
    return false;
  }
};

// Force clear all authentication data (for debugging)
export const forceLogout = () => {
  console.log('Force clearing all localStorage data...');
  localStorage.clear();
  window.location.reload();
};

// Create reading plan on backend server
export const createReadingPlanOnServer = async (userId, days) => {
  try {
    const response = await fetch(`${TASKS_BASE_URL}/tasks/create-reading-plan?userId=${userId}&days=${days}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to create reading plan on server: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plan created on server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error creating reading plan on server:', error);
    throw error;
  }
};

// Update daily reading progress
export const updateReadingProgress = async (taskId, taskData) => {
  try {
    const requestBody = {
      id: taskId,
      userId: taskData.userId,
      dayNumber: taskData.dayNumber,
      startPage: taskData.startPage,
      endPage: taskData.endPage,
      taskDate: new Date().toISOString(),
      progress: taskData.progress,
      surahRange: taskData.surahRange || "",
      completed: false
    };

    const response = await fetch(`${TASKS_BASE_URL}/tasks/${taskId}/progress`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Failed to update reading progress: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading progress updated on server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error updating reading progress:', error);
    throw error;
  }
};

// Mark reading plan as complete
export const completeReadingPlan = async (taskId, taskData) => {
  try {
    const requestBody = {
      id: taskId,
      userId: taskData.userId,
      dayNumber: taskData.dayNumber,
      startPage: taskData.startPage,
      endPage: taskData.endPage,
      taskDate: new Date().toISOString(),
      progress: taskData.progress,
      surahRange: taskData.surahRange || "",
      completed: true
    };

    const response = await fetch(`${TASKS_BASE_URL}/tasks/${taskId}/complete`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Failed to complete reading plan: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plan completed on server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error completing reading plan:', error);
    throw error;
  }
};

// Get user's active reading plan (returns array of tasks)
export const getUserReadingPlan = async (userId) => {
  try {
    const response = await fetch(`${TASKS_BASE_URL}/tasks/user/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get user reading plan: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ User reading plan fetched from server:', result);

    // Result is an array of tasks, return it as is
    return Array.isArray(result) ? result : [];
  } catch (error) {
    console.error('❌ Error fetching user reading plan:', error);
    throw error;
  }
};

// Delete/cancel user's reading plan
export const deleteUserReadingPlan = async (userId) => {
  try {
    const response = await fetch(`${TASKS_BASE_URL}/tasks/user/${userId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete reading plan: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plan deleted from server:', result);
    return result;
  } catch (error) {
    console.error('❌ Error deleting reading plan:', error);
    throw error;
  }
};

// Get user's completed reading plans summary
export const getUserReadingPlansSummary = async (userId) => {
  try {
    const response = await fetch(`${TASKS_BASE_URL}/tasks/user/${userId}/summary`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get reading plans summary: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Reading plans summary fetched from server:', result);

    // Result is a summary object with statistics
    return {
      totalTasks: result.totalTasks || 0,
      completedTasks: result.completedTasks || 0,
      remainingTasks: result.remainingTasks || 0,
      totalPages: result.totalPages || 0,
      completionPercentage: result.completionPercentage || 0
    };
  } catch (error) {
    console.error('❌ Error fetching reading plans summary:', error);
    throw error;
  }
};

// Sync server data with local data after login
export const syncUserDataAfterLogin = async (userId) => {
  try {
    console.log('🔄 Syncing user data after login for user:', userId);

    // Get user's active reading plan from server
    const serverTasks = await getUserReadingPlan(userId);
    console.log('📋 Server tasks:', serverTasks);

    if (serverTasks && serverTasks.length > 0) {
      // Convert server tasks to local plan format
      const serverPlan = convertServerTasksToLocalPlan(serverTasks);

      // Check if we have a local plan
      const localPlan = JSON.parse(localStorage.getItem('quranReadingPlan') || 'null');

      if (!localPlan || shouldUpdateLocalPlan(localPlan, serverPlan)) {
        // Update local plan with server data
        localStorage.setItem('quranReadingPlan', JSON.stringify(serverPlan));
        console.log('✅ Local plan updated with server data');
        return serverPlan;
      } else {
        console.log('📝 Local plan is up to date');
        return localPlan;
      }
    } else {
      console.log('📝 No active plan found on server');
      return null;
    }
  } catch (error) {
    console.error('❌ Error syncing user data:', error);
    // Return local plan as fallback
    const localPlan = JSON.parse(localStorage.getItem('quranReadingPlan') || 'null');
    return localPlan;
  }
};

// Convert server tasks array to local plan format
const convertServerTasksToLocalPlan = (serverTasks) => {
  if (!serverTasks || serverTasks.length === 0) return null;

  // Sort tasks by day number
  const sortedTasks = serverTasks.sort((a, b) => a.dayNumber - b.dayNumber);

  // Get plan metadata from first task
  const firstTask = sortedTasks[0];

  // Calculate plan details
  const totalDays = Math.max(...sortedTasks.map(t => t.dayNumber));
  const completedDays = {};

  // Map completed tasks
  sortedTasks.forEach(task => {
    if (task.completed) {
      completedDays[task.dayNumber - 1] = true;
    }
  });

  const completedCount = Object.values(completedDays).filter(Boolean).length;
  const completionPercentage = Math.round((completedCount / totalDays) * 100);

  return {
    taskId: firstTask.id, // Use first task ID as plan ID
    totalDays: totalDays,
    pagesPerDay: Math.ceil(604 / totalDays), // Calculate from total pages
    startDate: firstTask.taskDate,
    completedDays: completedDays,
    completionPercentage: completionPercentage,
    duration: totalDays,
    durationType: 'days',
    createdAt: firstTask.taskDate,
    lastSyncedAt: new Date().toISOString()
  };
};

// Check if local plan should be updated with server data
const shouldUpdateLocalPlan = (localPlan, serverPlan) => {
  if (!localPlan || !serverPlan) return true;

  // Update if server has more completed days
  const localCompleted = Object.values(localPlan.completedDays || {}).filter(Boolean).length;
  const serverCompleted = Object.values(serverPlan.completedDays || {}).filter(Boolean).length;

  return serverCompleted > localCompleted;
};

// Test all API endpoints for debugging
export const testAllAPIEndpoints = async (userId, taskId = null) => {
  console.log('🧪 Testing all API endpoints...');
  const results = {};

  try {
    // Test 1: Get user reading plan
    console.log('📋 Testing GET /tasks/user/{userId}...');
    try {
      const userTasks = await getUserReadingPlan(userId);
      results.getUserTasks = { success: true, data: userTasks };
      console.log('✅ GET user tasks successful:', userTasks);
    } catch (error) {
      results.getUserTasks = { success: false, error: error.message };
      console.error('❌ GET user tasks failed:', error);
    }

    // Test 2: Get user summary
    console.log('📊 Testing GET /tasks/user/{userId}/summary...');
    try {
      const summary = await getUserReadingPlansSummary(userId);
      results.getUserSummary = { success: true, data: summary };
      console.log('✅ GET user summary successful:', summary);
    } catch (error) {
      results.getUserSummary = { success: false, error: error.message };
      console.error('❌ GET user summary failed:', error);
    }

    // Test 3: Create reading plan
    console.log('📝 Testing POST /tasks/create-reading-plan...');
    try {
      const newPlan = await createReadingPlanOnServer(userId, 30);
      results.createPlan = { success: true, data: newPlan };
      console.log('✅ POST create plan successful:', newPlan);

      // Use the created plan's task ID for further tests
      if (newPlan && newPlan.id) {
        taskId = newPlan.id;
      }
    } catch (error) {
      results.createPlan = { success: false, error: error.message };
      console.error('❌ POST create plan failed:', error);
    }

    // Test 4: Update progress (only if we have a task ID)
    if (taskId) {
      console.log('📈 Testing PUT /tasks/{taskId}/progress...');
      try {
        const taskData = {
          userId: userId,
          dayNumber: 1,
          startPage: 604,
          endPage: 595,
          progress: 10,
          surahRange: "Al-Fatihah"
        };

        const progressResult = await updateReadingProgress(taskId, taskData);
        results.updateProgress = { success: true, data: progressResult };
        console.log('✅ PUT update progress successful:', progressResult);
      } catch (error) {
        results.updateProgress = { success: false, error: error.message };
        console.error('❌ PUT update progress failed:', error);
      }

      // Test 5: Complete plan
      console.log('🎯 Testing PUT /tasks/{taskId}/complete...');
      try {
        const completionData = {
          userId: userId,
          dayNumber: 30,
          startPage: 20,
          endPage: 1,
          progress: 20,
          surahRange: "Al-Baqarah"
        };

        const completionResult = await completeReadingPlan(taskId, completionData);
        results.completePlan = { success: true, data: completionResult };
        console.log('✅ PUT complete plan successful:', completionResult);
      } catch (error) {
        results.completePlan = { success: false, error: error.message };
        console.error('❌ PUT complete plan failed:', error);
      }
    } else {
      console.log('⚠️ Skipping progress and completion tests - no task ID available');
      results.updateProgress = { success: false, error: 'No task ID available' };
      results.completePlan = { success: false, error: 'No task ID available' };
    }

    // Test 6: Delete plan
    console.log('🗑️ Testing DELETE /tasks/user/{userId}...');
    try {
      const deleteResult = await deleteUserReadingPlan(userId);
      results.deletePlan = { success: true, data: deleteResult };
      console.log('✅ DELETE plan successful:', deleteResult);
    } catch (error) {
      results.deletePlan = { success: false, error: error.message };
      console.error('❌ DELETE plan failed:', error);
    }

  } catch (error) {
    console.error('❌ API testing failed:', error);
  }

  // Summary
  console.log('🧪 API Test Results Summary:');
  Object.entries(results).forEach(([test, result]) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${test}: ${result.success ? 'PASSED' : 'FAILED'}`);
    if (!result.success) {
      console.log(`   Error: ${result.error}`);
    }
  });

  return results;
};
