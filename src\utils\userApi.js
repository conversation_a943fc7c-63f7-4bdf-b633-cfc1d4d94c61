// User API utilities for fetching user data and handling image uploads

const BASE_URL = 'https://kotaby.duckdns.org';

// Get user data from localStorage
export const getStoredUserData = () => {
  try {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing stored user data:', error);
    return null;
  }
};

// Fetch user data from API by ID
export const fetchUserById = async (userId) => {
  try {
    const response = await fetch(`${BASE_URL}/users/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user data: ${response.status}`);
    }

    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

// Upload user profile image
export const uploadUserImage = async (userId, imageFile) => {
  try {
    const formData = new FormData();
    formData.append('file', imageFile);

    const response = await fetch(`${BASE_URL}/users/${userId}/photo`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Failed to upload image: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

// Get user profile image URL
export const getUserImageUrl = (userId, imagePath) => {
  if (!imagePath) return null;
  
  // If imagePath is already a full URL, return it
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  
  // Otherwise, construct the full URL
  return `${BASE_URL}/users/${userId}/photo/${imagePath}`;
};

// Update user data in localStorage
export const updateStoredUserData = (userData) => {
  try {
    localStorage.setItem('user', JSON.stringify(userData));
    // Also update userData key for backward compatibility
    localStorage.setItem('userData', JSON.stringify(userData));
    return true;
  } catch (error) {
    console.error('Error updating stored user data:', error);
    return false;
  }
};

// Initialize user data on app load
export const initializeUserData = async () => {
  try {
    const storedUser = getStoredUserData();
    if (!storedUser || !storedUser.id) {
      console.warn('No valid user data found in localStorage');
      return null;
    }

    // Fetch fresh user data from API
    const freshUserData = await fetchUserById(storedUser.id);
    
    // Update localStorage with fresh data
    updateStoredUserData(freshUserData);
    
    return freshUserData;
  } catch (error) {
    console.error('Error initializing user data:', error);
    // Return stored data as fallback
    return getStoredUserData();
  }
};

// Check if user is authenticated
export const isUserAuthenticated = () => {
  try {
    const userData = getStoredUserData();
    // Check if userData exists and has required fields
    const isValid = userData &&
                   userData.id &&
                   userData.email &&
                   userData.userName;

    console.log('Authentication check:', { userData, isValid });
    return isValid;
  } catch (error) {
    console.error('Error in isUserAuthenticated:', error);
    return false;
  }
};

// Logout user
export const logoutUser = () => {
  try {
    console.log('Clearing all user data from localStorage...');
    localStorage.removeItem('user');
    localStorage.removeItem('userData');
    // Clear any other potential auth-related data
    localStorage.clear();
    console.log('User data cleared successfully');
    return true;
  } catch (error) {
    console.error('Error during logout:', error);
    return false;
  }
};

// Force clear all authentication data (for debugging)
export const forceLogout = () => {
  console.log('Force clearing all localStorage data...');
  localStorage.clear();
  window.location.reload();
};
