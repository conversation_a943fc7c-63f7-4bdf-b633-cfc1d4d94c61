// Test timezone functionality
const getCurrentDateInCairo = () => {
  const now = new Date();
  // Convert to Cairo timezone (UTC+2 or UTC+3 depending on DST)
  const cairoTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
  return cairoTime;
};

const getCairoDateString = (date = null) => {
  const targetDate = date || getCurrentDateInCairo();
  const cairoDate = new Date(targetDate.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
  return cairoDate.toISOString().split('T')[0];
};

console.log('Testing Cairo timezone functionality:');
console.log('Current UTC time:', new Date().toISOString());
console.log('Current Cairo time:', getCurrentDateInCairo().toISOString());
console.log('Cairo date string:', getCairoDateString());

// Test with a specific date
const testDate = new Date('2024-06-11T22:00:00Z'); // 10 PM UTC on June 11
console.log('\nTesting with specific date (June 11, 10 PM UTC):');
console.log('UTC date string:', testDate.toISOString().split('T')[0]);
console.log('Cairo date string:', getCairoDateString(testDate));

console.log('\n✅ Timezone tests completed!');
