// Test file for Quran Plan Utilities
// Simple manual tests to verify functionality

// Test data
const TOTAL_QURAN_PAGES = 604;

// Test calculatePagesPerDay
const calculatePagesPerDay = (duration, durationType) => {
  const totalDays = durationType === 'months' ? duration * 30 : duration;
  return Math.ceil(TOTAL_QURAN_PAGES / totalDays);
};

// Test calculateTotalDays
const calculateTotalDays = (duration, durationType) => {
  return durationType === 'months' ? duration * 30 : duration;
};

// Run tests
console.log('Testing calculatePagesPerDay:');
console.log('30 days:', calculatePagesPerDay(30, 'days')); // Should be ~21 pages per day
console.log('2 months:', calculatePagesPerDay(2, 'months')); // Should be ~11 pages per day
console.log('6 months:', calculatePagesPerDay(6, 'months')); // Should be ~4 pages per day

console.log('\nTesting calculateTotalDays:');
console.log('30 days:', calculateTotalDays(30, 'days')); // Should be 30
console.log('2 months:', calculateTotalDays(2, 'months')); // Should be 60

console.log('\n✅ Basic utility function tests completed!');
console.log('\nExpected results:');
console.log('- 30 days: ~21 pages per day (604/30 = 20.13, rounded up to 21)');
console.log('- 2 months (60 days): ~11 pages per day (604/60 = 10.07, rounded up to 11)');
console.log('- 6 months (180 days): ~4 pages per day (604/180 = 3.36, rounded up to 4)');
