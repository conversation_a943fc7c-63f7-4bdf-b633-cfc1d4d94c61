// Notification Service Manager
class NotificationService {
  constructor() {
    this.serviceWorker = null;
    this.isServiceWorkerReady = false;
    this.messageHandlers = new Map();
  }

  // Initialize the service worker
  async init() {
    if ('serviceWorker' in navigator) {
      try {
        // Register the service worker
        const registration = await navigator.serviceWorker.register('/duaa-service-worker.js', {
          scope: '/'
        });

        console.log('Duaa Service Worker registered:', registration);

        // Wait for the service worker to be ready
        await navigator.serviceWorker.ready;
        this.isServiceWorkerReady = true;

        // Set up message listener
        navigator.serviceWorker.addEventListener('message', (event) => {
          this.handleServiceWorkerMessage(event);
        });

        // Get the active service worker
        this.serviceWorker = registration.active || registration.waiting || registration.installing;

        // Listen for service worker state changes
        if (this.serviceWorker) {
          this.serviceWorker.addEventListener('statechange', () => {
            if (this.serviceWorker.state === 'activated') {
              this.isServiceWorkerReady = true;
            }
          });
        }

        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        throw error;
      }
    } else {
      throw new Error('Service Workers not supported');
    }
  }

  // Handle messages from service worker
  handleServiceWorkerMessage(event) {
    const { type, data } = event.data;
    
    switch (type) {
      case 'REQUEST_NOTIFICATION_STATUS':
        this.sendNotificationStatus();
        break;
      case 'DUAA_NOTIFICATIONS_STARTED':
        console.log('Duaa notifications started in service worker');
        break;
      case 'DUAA_NOTIFICATIONS_STOPPED':
        console.log('Duaa notifications stopped in service worker');
        break;
      default:
        // Handle custom message handlers
        if (this.messageHandlers.has(type)) {
          this.messageHandlers.get(type)(data);
        }
    }
  }

  // Send message to service worker
  async sendMessage(type, data = null) {
    if (!this.isServiceWorkerReady) {
      console.warn('Service worker not ready, queuing message:', type);
      // Wait a bit and try again
      setTimeout(() => this.sendMessage(type, data), 1000);
      return;
    }

    if (navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({ type, data });
    } else {
      console.warn('No service worker controller available');
    }
  }

  // Start Duaa notifications
  async startDuaaNotifications() {
    console.log('🔔 NotificationService: Starting Duaa notifications...');

    // Check permission first
    if (Notification.permission !== 'granted') {
      console.error('❌ Notification permission not granted');
      throw new Error('Notification permission not granted');
    }

    await this.sendMessage('START_DUAA_NOTIFICATIONS');
    console.log('✅ NotificationService: Start message sent to service worker');
  }

  // Stop Duaa notifications
  async stopDuaaNotifications() {
    console.log('🛑 NotificationService: Stopping Duaa notifications...');
    await this.sendMessage('STOP_DUAA_NOTIFICATIONS');
    console.log('✅ NotificationService: Stop message sent to service worker');
  }

  // Send current notification status to service worker
  sendNotificationStatus() {
    try {
      const settings = localStorage.getItem('notificationSettings');
      if (settings) {
        const parsedSettings = JSON.parse(settings);
        const shouldStart = parsedSettings.enabled && parsedSettings.duaa;
        
        if (shouldStart) {
          this.startDuaaNotifications();
        } else {
          this.stopDuaaNotifications();
        }
      }
    } catch (error) {
      console.error('Error sending notification status:', error);
    }
  }

  // Check if notifications are supported
  static isSupported() {
    return 'serviceWorker' in navigator && 'Notification' in window;
  }

  // Request notification permission
  static async requestPermission() {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported');
    }

    const permission = await Notification.requestPermission();
    return permission;
  }

  // Get current notification permission
  static getPermission() {
    if ('Notification' in window) {
      return Notification.permission;
    }
    return 'default';
  }

  // Add custom message handler
  addMessageHandler(type, handler) {
    this.messageHandlers.set(type, handler);
  }

  // Remove custom message handler
  removeMessageHandler(type) {
    this.messageHandlers.delete(type);
  }

  // Unregister service worker (for cleanup)
  async unregister() {
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        if (registration.scope.includes('duaa-service-worker')) {
          await registration.unregister();
          console.log('Duaa Service Worker unregistered');
        }
      }
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

// Auto-initialize when module is loaded
if (typeof window !== 'undefined') {
  // Initialize service worker when the page loads
  window.addEventListener('load', async () => {
    try {
      await notificationService.init();
      console.log('Notification service initialized');
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
    }
  });
}

export default notificationService;
