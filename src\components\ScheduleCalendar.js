import { useState, useEffect } from 'react';
import { getPlanCalendarData } from '../utils/quranPlanUtils';

function ScheduleCalendar({ isOpen, onClose, planData }) {
  const [calendarData, setCalendarData] = useState([]);
  const [currentMonth, setCurrentMonth] = useState(new Date());

  useEffect(() => {
    if (planData && isOpen) {
      const data = getPlanCalendarData(planData);
      setCalendarData(data);
      
      // Set current month to plan start date
      if (planData.startDate) {
        setCurrentMonth(new Date(planData.startDate));
      }
    }
  }, [planData, isOpen]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#4CAF50'; // Green
      case 'missed':
        return '#F44336'; // Red
      case 'current':
        return '#FF9800'; // Orange
      case 'planned':
        return '#2196F3'; // Blue
      default:
        return '#E0E0E0'; // Gray
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'missed':
        return 'Missed';
      case 'current':
        return 'Today';
      case 'planned':
        return 'Planned';
      default:
        return '';
    }
  };

  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

 const formatDate = (date) => {
  const cairoDate = new Date(
    date.toLocaleString('en-US', { timeZone: 'Africa/Cairo' })
  );
  return cairoDate.toISOString().split('T')[0]; // Returns 'YYYY-MM-DD'
};


  const navigateMonth = (direction) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + direction);
    setCurrentMonth(newMonth);
  };

  const renderCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDay = getFirstDayOfMonth(currentMonth);
    const days = [];

    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(
        <div key={`empty-${i}`} style={{
          width: '40px',
          height: '40px',
          margin: '2px'
        }} />
      );
    }

    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      const dateString = formatDate(date);
      const dayData = calendarData.find(item => item.date === dateString);

      days.push(
        <div
          key={day}
          style={{
            width: '40px',
            height: '40px',
            margin: '2px',
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            fontWeight: 'bold',
            cursor: dayData ? 'pointer' : 'default',
            backgroundColor: dayData ? getStatusColor(dayData.status) : '#F5F5F5',
            color: dayData ? 'white' : '#666',
            border: dayData ? '2px solid rgba(255,255,255,0.3)' : '1px solid #E0E0E0',
            transition: 'all 0.3s ease',
            position: 'relative'
          }}
          title={dayData ? 
            `Day ${dayData.dayNumber}: Pages ${dayData.endPage}-${dayData.startPage} (${getStatusText(dayData.status)})` : 
            ''
          }
          onMouseEnter={(e) => {
            if (dayData) {
              e.target.style.transform = 'scale(1.1)';
              e.target.style.zIndex = '10';
            }
          }}
          onMouseLeave={(e) => {
            if (dayData) {
              e.target.style.transform = 'scale(1)';
              e.target.style.zIndex = '1';
            }
          }}
        >
          {day}
        </div>
      );
    }

    return days;
  };

  if (!isOpen) return null;

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const completedDays = calendarData.filter(day => day.status === 'completed').length;
  const missedDays = calendarData.filter(day => day.status === 'missed').length;
  const totalDays = calendarData.length;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '15px',
        padding: '25px',
        maxWidth: '600px',
        width: '90%',
        maxHeight: '80vh',
        overflowY: 'auto',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
        border: '2px solid #d4af37'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <h2 style={{
            color: '#d4af37',
            fontSize: '24px',
            fontWeight: 'bold',
            margin: 0
          }}>
            Reading Schedule
          </h2>
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: '24px',
              color: '#666',
              cursor: 'pointer',
              padding: '5px'
            }}
          >
            ×
          </button>
        </div>

        {/* Progress Summary */}
        <div style={{
          backgroundColor: '#FFF8E1',
          padding: '15px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #d4af37'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
            <div>
              <div style={{ color: '#4CAF50', fontSize: '20px', fontWeight: 'bold' }}>
                {completedDays}
              </div>
              <div style={{ color: '#666', fontSize: '12px' }}>Completed</div>
            </div>
            <div>
              <div style={{ color: '#F44336', fontSize: '20px', fontWeight: 'bold' }}>
                {missedDays}
              </div>
              <div style={{ color: '#666', fontSize: '12px' }}>Missed</div>
            </div>
            <div>
              <div style={{ color: '#2196F3', fontSize: '20px', fontWeight: 'bold' }}>
                {totalDays - completedDays - missedDays}
              </div>
              <div style={{ color: '#666', fontSize: '12px' }}>Remaining</div>
            </div>
          </div>
        </div>

        {/* Calendar Navigation */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '15px'
        }}>
          <button
            onClick={() => navigateMonth(-1)}
            style={{
              backgroundColor: '#d4af37',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 12px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            ← Prev
          </button>
          <h3 style={{
            color: '#8B4513',
            fontSize: '18px',
            fontWeight: 'bold',
            margin: 0
          }}>
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </h3>
          <button
            onClick={() => navigateMonth(1)}
            style={{
              backgroundColor: '#d4af37',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 12px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Next →
          </button>
        </div>

        {/* Calendar Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(7, 1fr)',
          gap: '2px',
          marginBottom: '20px'
        }}>
          {/* Day headers */}
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} style={{
              padding: '8px',
              textAlign: 'center',
              fontWeight: 'bold',
              color: '#8B4513',
              fontSize: '12px'
            }}>
              {day}
            </div>
          ))}
          
          {/* Calendar days */}
          {renderCalendarDays()}
        </div>

        {/* Legend */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '15px',
          flexWrap: 'wrap'
        }}>
          {[
            { status: 'completed', label: 'Completed' },
            { status: 'missed', label: 'Missed' },
            { status: 'current', label: 'Today' },
            { status: 'planned', label: 'Planned' }
          ].map(item => (
            <div key={item.status} style={{
              display: 'flex',
              alignItems: 'center',
              gap: '5px'
            }}>
              <div style={{
                width: '12px',
                height: '12px',
                borderRadius: '3px',
                backgroundColor: getStatusColor(item.status)
              }} />
              <span style={{ fontSize: '12px', color: '#666' }}>
                {item.label}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default ScheduleCalendar;
