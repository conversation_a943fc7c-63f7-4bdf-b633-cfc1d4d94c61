// Test API functionality
const BASE_URL = 'https://kotaby.duckdns.org';

// Test user API endpoint
const testUserAPI = async () => {
  try {
    console.log('Testing user API...');
    
    // Test with a sample user ID (you can replace this with a real ID)
    const testUserId = 1;
    
    const response = await fetch(`${BASE_URL}/users/${testUserId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);

    if (response.ok) {
      const userData = await response.json();
      console.log('User data received:', userData);
    } else {
      console.log('API request failed:', response.statusText);
    }
  } catch (error) {
    console.error('Error testing API:', error);
  }
};

// Test image upload endpoint structure
const testImageUploadEndpoint = () => {
  const testUserId = 1;
  const uploadUrl = `${BASE_URL}/users/${testUserId}/photo`;
  console.log('Image upload URL would be:', uploadUrl);
};

console.log('API Test Results:');
console.log('================');
testUserAPI();
testImageUploadEndpoint();

export { testUserAPI, testImageUploadEndpoint };
