// Duaa Notification Service Worker
const DUAA_API_URL = 'https://alquran.vip/APIs/duas';
const NOTIFICATION_INTERVAL = 60 * 60 * 1000; // 1 hour in milliseconds

let duaaNotificationTimer = null;

// Install event
self.addEventListener('install', (event) => {
  console.log('Duaa Service Worker installing...');
  self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Duaa Service Worker activating...');
  event.waitUntil(self.clients.claim());
  
  // Check if notifications should be started
  checkAndStartNotifications();
});

// Message event to handle commands from main thread
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'START_DUAA_NOTIFICATIONS':
      startDuaaNotifications();
      break;
    case 'STOP_DUAA_NOTIFICATIONS':
      stopDuaaNotifications();
      break;
    case 'CHECK_NOTIFICATION_STATUS':
      event.ports[0].postMessage({
        type: 'NOTIFICATION_STATUS',
        isActive: duaaNotificationTimer !== null
      });
      break;
  }
});

// Function to fetch a random Duaa from the API
async function fetchRandomDuaa() {
  try {
    const response = await fetch(DUAA_API_URL);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Check if data is an array and has items
    if (Array.isArray(data) && data.length > 0) {
      // Get a random Duaa from the array
      const randomIndex = Math.floor(Math.random() * data.length);
      const randomDuaa = data[randomIndex];
      
      // Return the text field
      return randomDuaa.text || 'Default Duaa text';
    } else {
      throw new Error('No Duaa data available');
    }
  } catch (error) {
    console.error('Error fetching Duaa:', error);
    // Return a default Duaa if API fails
    return 'اللَّهُمَّ أَعِنِّي عَلَى ذِكْرِكَ وَشُكْرِكَ وَحُسْنِ عِبَادَتِكَ';
  }
}

// Function to show Duaa notification
async function showDuaaNotification() {
  try {
    const duaaText = await fetchRandomDuaa();
    
    const notificationOptions = {
      body: duaaText,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: 'duaa-notification',
      requireInteraction: false,
      silent: false,
      data: {
        type: 'duaa',
        timestamp: Date.now()
      }
    };
    
    await self.registration.showNotification('Duaa Reminder', notificationOptions);
    console.log('Duaa notification sent:', duaaText);
  } catch (error) {
    console.error('Error showing Duaa notification:', error);
  }
}

// Function to start Duaa notifications
function startDuaaNotifications() {
  // Clear any existing timer
  if (duaaNotificationTimer) {
    clearInterval(duaaNotificationTimer);
  }
  
  console.log('Starting Duaa notifications - every hour');
  
  // Show first notification immediately
  showDuaaNotification();
  
  // Set up recurring notifications every hour
  duaaNotificationTimer = setInterval(() => {
    showDuaaNotification();
  }, NOTIFICATION_INTERVAL);
  
  // Store the timer state
  self.postMessage({ type: 'DUAA_NOTIFICATIONS_STARTED' });
}

// Function to stop Duaa notifications
function stopDuaaNotifications() {
  if (duaaNotificationTimer) {
    clearInterval(duaaNotificationTimer);
    duaaNotificationTimer = null;
    console.log('Duaa notifications stopped');
  }
  
  // Clear any existing Duaa notifications
  self.registration.getNotifications({ tag: 'duaa-notification' })
    .then(notifications => {
      notifications.forEach(notification => notification.close());
    });
  
  self.postMessage({ type: 'DUAA_NOTIFICATIONS_STOPPED' });
}

// Function to check notification settings and start if needed
async function checkAndStartNotifications() {
  try {
    // This will be called when the service worker starts
    // We'll check with the main thread about notification settings
    const clients = await self.clients.matchAll();
    if (clients.length > 0) {
      clients[0].postMessage({ type: 'REQUEST_NOTIFICATION_STATUS' });
    }
  } catch (error) {
    console.error('Error checking notification status:', error);
  }
}

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  // Focus or open the app when notification is clicked
  event.waitUntil(
    self.clients.matchAll({ type: 'window' }).then((clients) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clients) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no window/tab is open, open a new one
      if (self.clients.openWindow) {
        return self.clients.openWindow('/');
      }
    })
  );
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('Duaa notification closed');
});

// Periodic background sync (if supported)
self.addEventListener('sync', (event) => {
  if (event.tag === 'duaa-notification-sync') {
    event.waitUntil(checkAndStartNotifications());
  }
});

console.log('Duaa Service Worker loaded');
