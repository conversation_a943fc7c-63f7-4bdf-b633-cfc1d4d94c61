// Duaa Notification Service Worker
const DUAA_API_URL = 'https://alquran.vip/APIs/duas';
const NOTIFICATION_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds

let duaaNotificationTimer = null;

// Install event
self.addEventListener('install', (event) => {
  console.log('Duaa Service Worker installing...');
  self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Duaa Service Worker activating...');
  event.waitUntil(self.clients.claim());
  
  // Check if notifications should be started
  checkAndStartNotifications();
});

// Message event to handle commands from main thread
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'START_DUAA_NOTIFICATIONS':
      startDuaaNotifications();
      break;
    case 'STOP_DUAA_NOTIFICATIONS':
      stopDuaaNotifications();
      break;
    case 'CHECK_NOTIFICATION_STATUS':
      event.ports[0].postMessage({
        type: 'NOTIFICATION_STATUS',
        isActive: duaaNotificationTimer !== null
      });
      break;
  }
});

// Predefined Duaa collection as fallback
const FALLBACK_DUAS = [
  'اللَّهُمَّ أَعِنِّي عَلَى ذِكْرِكَ وَشُكْرِكَ وَحُسْنِ عِبَادَتِكَ\n\n(O Allah, help me to remember You, thank You, and worship You in the best manner)',
  'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ\n\n(Our Lord, give us good in this world and good in the next world, and save us from the punishment of the Fire)',
  'اللَّهُمَّ اغْفِرْ لِي ذَنْبِي وَوَسِّعْ لِي فِي دَارِي وَبَارِكْ لِي فِي رِزْقِي\n\n(O Allah, forgive my sins, expand my home for me, and bless my sustenance)',
  'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْهُدَى وَالتُّقَى وَالْعَفَافَ وَالْغِنَى\n\n(O Allah, I ask You for guidance, piety, chastity and contentment)',
  'اللَّهُمَّ أَصْلِحْ لِي دِينِي الَّذِي هُوَ عِصْمَةُ أَمْرِي\n\n(O Allah, make my religion good for me, which is the safeguard of my affairs)',
  'رَبِّ اشْرَحْ لِي صَدْرِي وَيَسِّرْ لِي أَمْرِي\n\n(My Lord, expand for me my breast and ease for me my task)',
  'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْهَمِّ وَالْحَزَنِ\n\n(O Allah, I seek refuge in You from anxiety and sorrow)',
  'اللَّهُمَّ بَارِكْ لَنَا فِيمَا رَزَقْتَنَا وَقِنَا عَذَابَ النَّارِ\n\n(O Allah, bless us in what You have provided us and protect us from the punishment of the Fire)'
];

// Function to fetch a random Duaa from the API or fallback
async function fetchRandomDuaa() {
  console.log('🌐 Attempting to fetch Duaa from API:', DUAA_API_URL);

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    // Try to fetch from API with no-cors mode to avoid CORS issues
    const response = await fetch(DUAA_API_URL, {
      signal: controller.signal,
      mode: 'no-cors'
    });

    clearTimeout(timeoutId);

    // Since we're using no-cors, we can't read the response
    // So we'll fall back to our predefined collection
    throw new Error('Using fallback due to CORS restrictions');

  } catch (error) {
    console.log('🔄 API unavailable, using predefined Duaa collection:', error.message);

    // Use predefined Duaa collection
    const randomIndex = Math.floor(Math.random() * FALLBACK_DUAS.length);
    const selectedDuaa = FALLBACK_DUAS[randomIndex];

    console.log('🎲 Selected Duaa from collection:', { index: randomIndex, duaa: selectedDuaa });
    return selectedDuaa;
  }
}

// Function to show Duaa notification
async function showDuaaNotification() {
  try {
    console.log('🔔 Attempting to show system notification...');
    const duaaText = await fetchRandomDuaa();
    console.log('📝 Fetched Duaa text:', duaaText);

    // Check if we have permission first
    if (!self.Notification || Notification.permission !== 'granted') {
      console.error('❌ Notification permission not granted. Current permission:', Notification.permission);
      throw new Error('Notification permission not granted');
    }

    // Create notification options for system-level notification
    const notificationOptions = {
      body: duaaText,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: 'duaa-reminder',
      requireInteraction: false,
      silent: false,
      renotify: true,
      timestamp: Date.now(),
      data: {
        type: 'duaa',
        timestamp: Date.now()
      },
      // Remove any URL or origin references to avoid showing localhost
      dir: 'auto',
      lang: 'ar'
    };

    console.log('📱 Showing system notification with options:', notificationOptions);

    // Use service worker registration to show notification
    // This ensures it appears as a system-level notification
    const notification = await self.registration.showNotification('🤲 Duaa Reminder', notificationOptions);

    console.log('✅ System notification sent successfully!');
    console.log('📋 Notification details:', {
      title: '🤲 Duaa Reminder',
      body: duaaText,
      timestamp: new Date().toLocaleString()
    });

    return notification;

  } catch (error) {
    console.error('❌ Error showing system notification:', error);
    console.error('🔍 Error details:', {
      message: error.message,
      permission: Notification.permission,
      serviceWorkerRegistration: !!self.registration
    });
    throw error;
  }
}

// Function to start Duaa notifications
async function startDuaaNotifications() {
  // Clear any existing timer
  if (duaaNotificationTimer) {
    clearInterval(duaaNotificationTimer);
    duaaNotificationTimer = null;
  }

  console.log('🔔 Starting Duaa notifications - every 5 minutes');

  try {
    // Show first notification immediately for testing
    console.log('📱 Showing immediate test notification...');
    await showDuaaNotification();
    console.log('✅ Immediate notification sent successfully');

    // Set up recurring notifications every 5 minutes
    duaaNotificationTimer = setInterval(async () => {
      console.log('⏰ 5-minute Duaa notification trigger');
      try {
        await showDuaaNotification();
      } catch (error) {
        console.error('❌ Error in 5-minute notification:', error);
      }
    }, NOTIFICATION_INTERVAL);

    console.log('✅ Duaa notification timer set for every', NOTIFICATION_INTERVAL / 1000 / 60, 'minutes');

    // Notify main thread via clients
    const clients = await self.clients.matchAll();
    if (clients.length > 0) {
      clients[0].postMessage({ type: 'DUAA_NOTIFICATIONS_STARTED' });
    }
  } catch (error) {
    console.error('❌ Error starting Duaa notifications:', error);
    const clients = await self.clients.matchAll();
    if (clients.length > 0) {
      clients[0].postMessage({ type: 'DUAA_NOTIFICATIONS_ERROR', error: error.message });
    }
  }
}

// Function to stop Duaa notifications
function stopDuaaNotifications() {
  if (duaaNotificationTimer) {
    clearInterval(duaaNotificationTimer);
    duaaNotificationTimer = null;
    console.log('Duaa notifications stopped');
  }
  
  // Clear any existing Duaa notifications
  self.registration.getNotifications({ tag: 'duaa-notification' })
    .then(notifications => {
      notifications.forEach(notification => notification.close());
    });

  // Notify main thread via clients
  self.clients.matchAll().then(clients => {
    if (clients.length > 0) {
      clients[0].postMessage({ type: 'DUAA_NOTIFICATIONS_STOPPED' });
    }
  });
}

// Function to check notification settings and start if needed
async function checkAndStartNotifications() {
  try {
    // This will be called when the service worker starts
    // We'll check with the main thread about notification settings
    const clients = await self.clients.matchAll();
    if (clients.length > 0) {
      clients[0].postMessage({ type: 'REQUEST_NOTIFICATION_STATUS' });
    }
  } catch (error) {
    console.error('Error checking notification status:', error);
  }
}

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  // Focus or open the app when notification is clicked
  event.waitUntil(
    self.clients.matchAll({ type: 'window' }).then((clients) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clients) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no window/tab is open, open a new one
      if (self.clients.openWindow) {
        return self.clients.openWindow('/');
      }
    })
  );
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('Duaa notification closed');
});

// Periodic background sync (if supported)
self.addEventListener('sync', (event) => {
  if (event.tag === 'duaa-notification-sync') {
    event.waitUntil(checkAndStartNotifications());
  }
});

console.log('Duaa Service Worker loaded');
