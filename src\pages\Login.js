import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import BgImage from '../assets/bg.jpg';
import Lantern from '../assets/lantern.png';

function Login({ setIsAuthenticated }) {
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleToggle = () => setShowPassword(!showPassword);
  const navigate = useNavigate();







  const handleLogin = async (e) => {
    e.preventDefault();

    if (!email.trim() || !password.trim()) {
      alert("Please enter both email and password.");
      return;
    }

    try {
      const response = await fetch('https://kotaby.duckdns.org/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Invalid credentials');
      }

      const data = await response.json();
      console.log('Login successful:', data);

      // Optional: store user info (like token or user object)
      localStorage.setItem('user', JSON.stringify(data));

      if (setIsAuthenticated) setIsAuthenticated(true);
      navigate("/home");

    } catch (error) {
      console.error('Login failed:', error);
      alert('Login failed: ' + error.message);
    }
  };









  // const handleLogin = (e) => {
  //   e.preventDefault();

  //   // Dummy login logic — you can replace with real auth check
  //   if (email.trim() && password.trim()) {
  //     console.log("Logged in with:", { email, password });
  //     if (setIsAuthenticated) setIsAuthenticated(true); // Optional
  //     navigate("/home");
  //   } else {
  //     alert("Please enter both email and password.");
  //   }
  // };


//  const handleLogin = async (e) => {
//   e.preventDefault();

//   if (!email.trim() || !password.trim()) {
//     alert("Please enter both email and password.");
//     return;
//   }

//   try {
//     const response = await fetch('https://kotaby.duckdns.org/users/login', {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json'
//       },
//       body: JSON.stringify({
//         id: 0,
//         userName: "",
//         email: email,
//         password: password,
//         image: "",
//         dateOfBirth: "2025-01-01T00:00:00.000Z",
//         nationality: ""
//       })
//     });

//     if (!response.ok) {
//       throw new Error('Invalid credentials');
//     }

//     const data = await response.json();
//     console.log('Login success:', data);

//     // Store user data in localStorage
//     localStorage.setItem('userData', JSON.stringify(data));
    
//     if (setIsAuthenticated) setIsAuthenticated(true);
//     navigate("/home");
//   } catch (error) {
//     console.error('Login failed:', error);
//     alert('Login failed: ' + error.message);
//   }
// };


  return (
    <div style={{
      minHeight: "100vh",
      backgroundImage: `url(${BgImage})`,
      backgroundSize: "cover",
      backgroundPosition: "center",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "40px",
      backdropFilter: "brightness(0.95)",
      position: "relative",
    }}>

      {/* Lantern */}
      <img
        src={Lantern}
        alt="Lantern"
        style={{
          position: "absolute",
          top: "5%",
          left: "50%",
          transform: "translateX(-50%)",
          width: "60px",
          zIndex: 1,
          animation: "floatLantern 3s ease-in-out infinite"
        }}
      />

      <motion.form
        onSubmit={handleLogin}
        initial={{ opacity: 0, scale: 0.9, rotateY: 15 }}
        animate={{ opacity: 1, scale: 1, rotateY: 0 }}
        exit={{ opacity: 0, scale: 0.9, rotateY: -15 }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
        style={{
          width: "100%",
          maxWidth: "420px",
          padding: "40px",
          borderRadius: "20px",
          background: "#fff",
          boxShadow: "0 0 15px rgba(212, 175, 55, 0.2)",
          border: "1px solid #f5e8b3",
          zIndex: 2,
          position: "relative"
        }}
      >
        <h2 style={{ textAlign: "center", marginBottom: "40px", color: "#d4af37", fontWeight: "bold" }}>
          Login to Continue
        </h2>

        {/* Email Input */}
        <div style={{ marginBottom: "25px" }}>
          <label style={{ color: "#666", marginBottom: "8px", display: "block" }}>Email address</label>
          <input
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            style={{
              width: "100%",
              padding: "12px",
              borderRadius: "10px",
              border: "1px solid #d4af37",
              outline: "none"
            }}
          />
        </div>

        {/* Password Input */}
        <div style={{ marginBottom: "15px", position: "relative" }}>
          <label style={{ color: "#666", marginBottom: "8px", display: "block" }}>Password</label>
          <input
            type={showPassword ? "text" : "password"}
            placeholder="Enter your password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            style={{
              width: "100%",
              padding: "12px",
              borderRadius: "10px",
              border: "1px solid #d4af37",
              outline: "none"
            }}
          />
          <span
            onClick={handleToggle}
            style={{
              position: "absolute",
              right: "15px",
              top: "38px",
              cursor: "pointer",
              fontSize: "13px",
              color: "#999"
            }}
          >
            {showPassword ? "Hide" : "Show"}
          </span>
        </div>

        {/* Forgot Password */}
        <p style={{ textAlign: "center", color: "#999", marginBottom: "30px", cursor: "pointer" }}>
          Forgot Password?
        </p>

        {/* Login Button */}
        <button type="submit" style={{
          width: "100%",
          padding: "12px",
          borderRadius: "30px",
          background: "linear-gradient(135deg, #d4af37, #f9e79f)",
          border: "none",
          fontWeight: "bold",
          color: "#fff",
          fontSize: "16px",
          cursor: "pointer",
          transition: "all 0.3s"
        }}
          onMouseOver={(e) => e.currentTarget.style.boxShadow = "0 0 8px rgba(212,175,55,0.3)"}
          onMouseOut={(e) => e.currentTarget.style.boxShadow = "none"}
        >
          Login
        </button>

        <p style={{ textAlign: "center", marginTop: "25px", color: "#000" }}>
          Don't have an account?{" "}
          <Link to="/signup" style={{
            color: "#d4af37",
            fontWeight: "bold",
            textDecoration: "none",
            transition: "color 0.2s"
          }}
            onMouseOver={(e) => (e.target.style.color = "#bfa131")}
            onMouseOut={(e) => (e.target.style.color = "#d4af37")}
          >
            Sign Up Now
          </Link>
        </p>
      </motion.form>
    </div>
  );
}

export default Login;
