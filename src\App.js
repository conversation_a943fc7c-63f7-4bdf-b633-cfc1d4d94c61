import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Home from './pages/Home';
import Hadith from './pages/Hadith';
import Surahs from './pages/Surahs';
import SurahAyat from './pages/SurahAyat';
import Tafser from './pages/Tafser';
import Tasmee from './pages/Tasmee';
import Navbar from './components/Navbar';
import { useState, useEffect } from 'react';
import { isUserAuthenticated } from './utils/userApi';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check authentication status on app load
  useEffect(() => {
    const checkAuth = () => {
      try {
        const authenticated = isUserAuthenticated();
        console.log('Authentication check result:', authenticated);
        setIsAuthenticated(authenticated);
      } catch (error) {
        console.error('Error checking authentication:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{
          color: '#d4af37',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          Loading...
        </div>
      </div>
    );
  }

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  return (
    <Router>
      {isAuthenticated && <Navbar onLogout={handleLogout} />}
      <Routes>
        <Route path="/" element={<Navigate to="/login" />} />
        <Route path="/login" element={<Login setIsAuthenticated={setIsAuthenticated} />} />
        <Route path="/signup" element={<Signup setIsAuthenticated={setIsAuthenticated} />} />
        <Route path="/home" element={isAuthenticated ? <Home /> : <Navigate to="/login" />} />
        <Route path="/hadith" element={isAuthenticated ? <Hadith /> : <Navigate to="/login" />} />
        <Route path="/surahs" element={isAuthenticated ? <Surahs /> : <Navigate to="/login" />} />
        <Route path="/surahayat/:id" element={isAuthenticated ? <SurahAyat /> : <Navigate to="/login" />} />
        <Route path="/tasmee" element={isAuthenticated ? <Tasmee /> : <Navigate to="/login" />} />
        <Route path="/tafser" element={isAuthenticated ? <Tafser /> : <Navigate to="/login" />} />
      </Routes>
    </Router>
  );
}

export default App;
