import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Home from './pages/Home';
import Hadith from './pages/Hadith';
import Surahs from './pages/Surahs';
import SurahAyat from './pages/SurahAyat';
import Tafser from './pages/Tafser';
import Navbar from './components/Navbar';
import { useState } from 'react';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  return (
    <Router>
      {isAuthenticated && <Navbar />}
      <Routes>
        <Route path="/" element={<Navigate to="/login" />} />
        <Route path="/login" element={<Login setIsAuthenticated={setIsAuthenticated} />} />
        <Route path="/signup" element={<Signup setIsAuthenticated={setIsAuthenticated} />} />
        <Route path="/home" element={isAuthenticated ? <Home /> : <Navigate to="/login" />} />
        <Route path="/hadith" element={isAuthenticated ? <Hadith /> : <Navigate to="/login" />} />
        <Route path="/surahs" element={isAuthenticated ? <Surahs /> : <Navigate to="/login" />} />
        <Route path="/surahayat/:id" element={isAuthenticated ? <SurahAyat /> : <Navigate to="/login" />} />
        <Route path="/tafser" element={isAuthenticated ? <Tafser /> : <Navigate to="/login" />} />
      </Routes>
    </Router>
  );
}

export default App;
