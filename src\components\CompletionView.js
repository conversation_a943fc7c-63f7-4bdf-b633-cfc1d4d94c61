import { useState } from 'react';
import ReadingTaskDisplay from './ReadingTaskDisplay';
import ScheduleCalendar from './ScheduleCalendar';
import { cancelReadingPlan } from '../utils/quranPlanUtils';
import { getStoredUserData, deleteUserReadingPlan, getUserReadingPlansSummary } from '../utils/userApi';

function CompletionView({
  isOpen,
  onClose,
  readingTask,
  planData,
  onTaskCompleted,
  onPlanCanceled
}) {
  const [showScheduleCalendar, setShowScheduleCalendar] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [showSummary, setShowSummary] = useState(false);
  const [summaryData, setSummaryData] = useState([]);
  const [loadingSummary, setLoadingSummary] = useState(false);

  const handleCancelPlan = () => {
    setShowCancelConfirm(true);
  };

  const confirmCancelPlan = async () => {
    try {
      // Get user data for backend call
      const userData = getStoredUserData();

      // Cancel plan locally
      const success = cancelReadingPlan();
      if (!success) {
        alert('Failed to cancel plan locally. Please try again.');
        return;
      }

      // Delete plan from backend
      if (userData && userData.id) {
        try {
          await deleteUserReadingPlan(userData.id);
          console.log('✅ Reading plan deleted from server');
        } catch (serverError) {
          console.error('⚠️ Failed to delete plan from server:', serverError);
          // Continue with local cancellation even if server fails
        }
      }

      onPlanCanceled();
      setShowCancelConfirm(false);
      onClose();
    } catch (error) {
      console.error('❌ Error canceling plan:', error);
      alert('Failed to cancel plan. Please try again.');
    }
  };

  const handleShowSummary = async () => {
    setLoadingSummary(true);
    try {
      const userData = getStoredUserData();
      if (userData && userData.id) {
        const summary = await getUserReadingPlansSummary(userData.id);
        setSummaryData(summary);
        setShowSummary(true);
      } else {
        alert('User not found. Please log in again.');
      }
    } catch (error) {
      console.error('❌ Error fetching summary:', error);
      alert('Failed to load reading plans summary. Please try again.');
    } finally {
      setLoadingSummary(false);
    }
  };

  const handleShowSchedule = () => {
    setShowScheduleCalendar(true);
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '15px',
        padding: '30px',
        maxWidth: '600px',
        width: '90%',
        maxHeight: '80vh',
        overflowY: 'auto',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
        border: '2px solid #d4af37'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '25px'
        }}>
          <h2 style={{
            color: '#d4af37',
            fontSize: '24px',
            fontWeight: 'bold',
            margin: 0
          }}>
            Reading Plan Progress
          </h2>
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: '24px',
              color: '#666',
              cursor: 'pointer',
              padding: '5px'
            }}
          >
            ×
          </button>
        </div>

        {/* Plan Summary */}
        {planData && (
          <div style={{
            backgroundColor: '#FFF8E1',
            padding: '20px',
            borderRadius: '10px',
            marginBottom: '25px',
            border: '1px solid #d4af37'
          }}>
            <h3 style={{ color: '#8B4513', marginBottom: '15px', fontSize: '18px' }}>
              Plan Overview
            </h3>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
              <div>
                <p style={{ margin: '5px 0', fontSize: '14px', color: '#666' }}>
                  <strong>Duration:</strong> {planData.duration} {planData.durationType}
                </p>
                <p style={{ margin: '5px 0', fontSize: '14px', color: '#666' }}>
                  <strong>Pages per Day:</strong> {planData.pagesPerDay}
                </p>
              </div>
              <div>
                <p style={{ margin: '5px 0', fontSize: '14px', color: '#666' }}>
                  <strong>Total Days:</strong> {planData.totalDays}
                </p>
                <p style={{ margin: '5px 0', fontSize: '14px', color: '#666' }}>
                  <strong>Progress:</strong> {planData.completionPercentage || 0}%
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Current Reading Task */}
        {readingTask && (
          <div style={{ marginBottom: '25px' }}>
            <ReadingTaskDisplay
              readingTask={readingTask}
              planData={planData}
              onTaskCompleted={onTaskCompleted}
              onShowSchedule={handleShowSchedule}
            />
          </div>
        )}

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          gap: '15px',
          justifyContent: 'center',
          flexWrap: 'wrap'
        }}>
          <button
            onClick={handleShowSchedule}
            style={{
              padding: '12px 25px',
              borderRadius: '8px',
              border: '2px solid #d4af37',
              backgroundColor: '#d4af37',
              color: 'white',
              fontSize: '16px',
              cursor: 'pointer',
              fontWeight: 'bold',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#bfa131';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#d4af37';
            }}
          >
            📅 View Full Schedule
          </button>
          
          <button
            onClick={handleShowSummary}
            disabled={loadingSummary}
            style={{
              padding: '12px 25px',
              borderRadius: '8px',
              border: '2px solid #2196F3',
              backgroundColor: 'white',
              color: '#2196F3',
              fontSize: '16px',
              cursor: loadingSummary ? 'not-allowed' : 'pointer',
              fontWeight: 'bold',
              transition: 'all 0.3s ease',
              opacity: loadingSummary ? 0.6 : 1
            }}
            onMouseEnter={(e) => {
              if (!loadingSummary) {
                e.target.style.backgroundColor = '#2196F3';
                e.target.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              if (!loadingSummary) {
                e.target.style.backgroundColor = 'white';
                e.target.style.color = '#2196F3';
              }
            }}
          >
            {loadingSummary ? '📊 Loading...' : '📊 View Summary'}
          </button>

          <button
            onClick={handleCancelPlan}
            style={{
              padding: '12px 25px',
              borderRadius: '8px',
              border: '2px solid #F44336',
              backgroundColor: 'white',
              color: '#F44336',
              fontSize: '16px',
              cursor: 'pointer',
              fontWeight: 'bold',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#F44336';
              e.target.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'white';
              e.target.style.color = '#F44336';
            }}
          >
            🗑️ Cancel Plan
          </button>
        </div>

        {/* Cancel Confirmation Dialog */}
        {showCancelConfirm && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1001
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: '10px',
              padding: '25px',
              maxWidth: '400px',
              width: '90%',
              textAlign: 'center',
              boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)'
            }}>
              <h3 style={{ color: '#F44336', marginBottom: '15px' }}>
                Cancel Reading Plan?
              </h3>
              <p style={{ color: '#666', marginBottom: '25px', fontSize: '14px' }}>
                Are you sure you want to cancel your current reading plan? 
                All progress will be lost and cannot be recovered.
              </p>
              <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
                <button
                  onClick={() => setShowCancelConfirm(false)}
                  style={{
                    padding: '10px 20px',
                    borderRadius: '6px',
                    border: '1px solid #ccc',
                    backgroundColor: 'white',
                    color: '#666',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  Keep Plan
                </button>
                <button
                  onClick={confirmCancelPlan}
                  style={{
                    padding: '10px 20px',
                    borderRadius: '6px',
                    border: 'none',
                    backgroundColor: '#F44336',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                >
                  Yes, Cancel Plan
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Schedule Calendar Modal */}
        <ScheduleCalendar
          isOpen={showScheduleCalendar}
          onClose={() => setShowScheduleCalendar(false)}
          planData={planData}
        />

        {/* Summary Modal */}
        {showSummary && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1001
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: '15px',
              padding: '30px',
              maxWidth: '600px',
              width: '90%',
              maxHeight: '80vh',
              overflowY: 'auto',
              boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
              border: '2px solid #d4af37'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '25px'
              }}>
                <h2 style={{
                  color: '#d4af37',
                  fontSize: '24px',
                  fontWeight: 'bold',
                  margin: 0
                }}>
                  📊 Reading Plans Summary
                </h2>
                <button
                  onClick={() => setShowSummary(false)}
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    fontSize: '24px',
                    cursor: 'pointer',
                    color: '#999',
                    padding: '5px'
                  }}
                >
                  ✕
                </button>
              </div>

              {summaryData && summaryData.length > 0 ? (
                <div>
                  <p style={{
                    color: '#666',
                    marginBottom: '20px',
                    textAlign: 'center'
                  }}>
                    Here are your completed reading plans:
                  </p>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                    {summaryData.map((plan, index) => (
                      <div key={index} style={{
                        backgroundColor: '#f8f9fa',
                        padding: '15px',
                        borderRadius: '10px',
                        border: '1px solid #e0e0e0'
                      }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <h4 style={{
                            color: '#4CAF50',
                            margin: 0,
                            fontSize: '16px',
                            fontWeight: 'bold'
                          }}>
                            ✅ Plan #{index + 1}
                          </h4>
                          <span style={{
                            backgroundColor: '#4CAF50',
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '12px',
                            fontSize: '12px',
                            fontWeight: 'bold'
                          }}>
                            COMPLETED
                          </span>
                        </div>

                        <div style={{ fontSize: '14px', color: '#666' }}>
                          <p style={{ margin: '4px 0' }}>
                            <strong>Duration:</strong> {plan.duration || 'N/A'} days
                          </p>
                          <p style={{ margin: '4px 0' }}>
                            <strong>Started:</strong> {plan.startDate ? new Date(plan.startDate).toLocaleDateString() : 'N/A'}
                          </p>
                          <p style={{ margin: '4px 0' }}>
                            <strong>Completed:</strong> {plan.completedDate ? new Date(plan.completedDate).toLocaleDateString() : 'N/A'}
                          </p>
                          <p style={{ margin: '4px 0' }}>
                            <strong>Pages Read:</strong> 604 pages
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 20px',
                  color: '#666'
                }}>
                  <div style={{ fontSize: '48px', marginBottom: '20px' }}>📚</div>
                  <h3 style={{ color: '#999', marginBottom: '10px' }}>No Completed Plans Yet</h3>
                  <p>Complete your first reading plan to see it here!</p>
                </div>
              )}

              <div style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '25px'
              }}>
                <button
                  onClick={() => setShowSummary(false)}
                  style={{
                    padding: '12px 25px',
                    borderRadius: '8px',
                    border: '2px solid #d4af37',
                    backgroundColor: '#d4af37',
                    color: 'white',
                    fontSize: '16px',
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#bfa131';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = '#d4af37';
                  }}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CompletionView;
