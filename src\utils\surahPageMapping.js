// Mapping of Surahs to their page ranges in the Quran
// This data maps each Surah to its starting and ending pages

export const SURAH_PAGE_MAPPING = {
  1: { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", arabicName: "الفاتحة", startPage: 1, endPage: 1 },
  2: { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", arabicName: "البقرة", startPage: 2, endPage: 49 },
  3: { name: "<PERSON> <PERSON><PERSON><PERSON><PERSON>", arabicName: "آل عمران", startPage: 50, endPage: 76 },
  4: { name: "An-<PERSON><PERSON>", arabicName: "النساء", startPage: 77, endPage: 106 },
  5: { name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", arabicName: "المائدة", startPage: 106, endPage: 127 },
  6: { name: "Al-An'am", arabicName: "الأنعام", startPage: 128, endPage: 151 },
  7: { name: "<PERSON><PERSON><PERSON><PERSON>raf", arabicName: "الأعراف", startPage: 151, endPage: 176 },
  8: { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", arabicName: "الأنفال", startPage: 177, endPage: 187 },
  9: { name: "At-Tawbah", arabicName: "التوبة", startPage: 187, endPage: 207 },
  10: { name: "Yunus", arabicName: "يونس", startPage: 208, endPage: 221 },
  11: { name: "Hud", arabicName: "هود", startPage: 221, endPage: 235 },
  12: { name: "Yusuf", arabicName: "يوسف", startPage: 235, endPage: 248 },
  13: { name: "Ar-Ra'd", arabicName: "الرعد", startPage: 249, endPage: 255 },
  14: { name: "Ibrahim", arabicName: "إبراهيم", startPage: 255, endPage: 261 },
  15: { name: "Al-Hijr", arabicName: "الحجر", startPage: 262, endPage: 267 },
  16: { name: "An-Nahl", arabicName: "النحل", startPage: 267, endPage: 281 },
  17: { name: "Al-Isra", arabicName: "الإسراء", startPage: 282, endPage: 293 },
  18: { name: "Al-Kahf", arabicName: "الكهف", startPage: 293, endPage: 304 },
  19: { name: "Maryam", arabicName: "مريم", startPage: 305, endPage: 312 },
  20: { name: "Taha", arabicName: "طه", startPage: 312, endPage: 322 },
  21: { name: "Al-Anbya", arabicName: "الأنبياء", startPage: 322, endPage: 331 },
  22: { name: "Al-Hajj", arabicName: "الحج", startPage: 332, endPage: 341 },
  23: { name: "Al-Mu'minun", arabicName: "المؤمنون", startPage: 342, endPage: 349 },
  24: { name: "An-Nur", arabicName: "النور", startPage: 350, endPage: 359 },
  25: { name: "Al-Furqan", arabicName: "الفرقان", startPage: 359, endPage: 366 },
  26: { name: "Ash-Shu'ara", arabicName: "الشعراء", startPage: 367, endPage: 377 },
  27: { name: "An-Naml", arabicName: "النمل", startPage: 377, endPage: 385 },
  28: { name: "Al-Qasas", arabicName: "القصص", startPage: 385, endPage: 396 },
  29: { name: "Al-'Ankabut", arabicName: "العنكبوت", startPage: 396, endPage: 404 },
  30: { name: "Ar-Rum", arabicName: "الروم", startPage: 404, endPage: 411 },
  31: { name: "Luqman", arabicName: "لقمان", startPage: 411, endPage: 414 },
  32: { name: "As-Sajdah", arabicName: "السجدة", startPage: 415, endPage: 417 },
  33: { name: "Al-Ahzab", arabicName: "الأحزاب", startPage: 418, endPage: 427 },
  34: { name: "Saba", arabicName: "سبأ", startPage: 428, endPage: 433 },
  35: { name: "Fatir", arabicName: "فاطر", startPage: 434, endPage: 440 },
  36: { name: "Ya-Sin", arabicName: "يس", startPage: 440, endPage: 445 },
  37: { name: "As-Saffat", arabicName: "الصافات", startPage: 446, endPage: 453 },
  38: { name: "Sad", arabicName: "ص", startPage: 453, endPage: 458 },
  39: { name: "Az-Zumar", arabicName: "الزمر", startPage: 458, endPage: 467 },
  40: { name: "Ghafir", arabicName: "غافر", startPage: 467, endPage: 477 },
  41: { name: "Fussilat", arabicName: "فصلت", startPage: 477, endPage: 482 },
  42: { name: "Ash-Shuraa", arabicName: "الشورى", startPage: 483, endPage: 489 },
  43: { name: "Az-Zukhruf", arabicName: "الزخرف", startPage: 489, endPage: 496 },
  44: { name: "Ad-Dukhan", arabicName: "الدخان", startPage: 496, endPage: 499 },
  45: { name: "Al-Jathiyah", arabicName: "الجاثية", startPage: 499, endPage: 502 },
  46: { name: "Al-Ahqaf", arabicName: "الأحقاف", startPage: 502, endPage: 506 },
  47: { name: "Muhammad", arabicName: "محمد", startPage: 507, endPage: 511 },
  48: { name: "Al-Fath", arabicName: "الفتح", startPage: 511, endPage: 515 },
  49: { name: "Al-Hujurat", arabicName: "الحجرات", startPage: 515, endPage: 518 },
  50: { name: "Qaf", arabicName: "ق", startPage: 518, endPage: 520 },
  51: { name: "Adh-Dhariyat", arabicName: "الذاريات", startPage: 520, endPage: 523 },
  52: { name: "At-Tur", arabicName: "الطور", startPage: 523, endPage: 526 },
  53: { name: "An-Najm", arabicName: "النجم", startPage: 526, endPage: 528 },
  54: { name: "Al-Qamar", arabicName: "القمر", startPage: 528, endPage: 531 },
  55: { name: "Ar-Rahman", arabicName: "الرحمن", startPage: 531, endPage: 534 },
  56: { name: "Al-Waqi'ah", arabicName: "الواقعة", startPage: 534, endPage: 537 },
  57: { name: "Al-Hadid", arabicName: "الحديد", startPage: 537, endPage: 542 },
  58: { name: "Al-Mujadila", arabicName: "المجادلة", startPage: 542, endPage: 545 },
  59: { name: "Al-Hashr", arabicName: "الحشر", startPage: 546, endPage: 549 },
  60: { name: "Al-Mumtahanah", arabicName: "الممتحنة", startPage: 549, endPage: 551 },
  61: { name: "As-Saff", arabicName: "الصف", startPage: 551, endPage: 553 },
  62: { name: "Al-Jumu'ah", arabicName: "الجمعة", startPage: 553, endPage: 554 },
  63: { name: "Al-Munafiqun", arabicName: "المنافقون", startPage: 554, endPage: 556 },
  64: { name: "At-Taghabun", arabicName: "التغابن", startPage: 556, endPage: 558 },
  65: { name: "At-Talaq", arabicName: "الطلاق", startPage: 558, endPage: 560 },
  66: { name: "At-Tahrim", arabicName: "التحريم", startPage: 560, endPage: 562 },
  67: { name: "Al-Mulk", arabicName: "الملك", startPage: 562, endPage: 564 },
  68: { name: "Al-Qalam", arabicName: "القلم", startPage: 564, endPage: 566 },
  69: { name: "Al-Haqqah", arabicName: "الحاقة", startPage: 566, endPage: 568 },
  70: { name: "Al-Ma'arij", arabicName: "المعارج", startPage: 568, endPage: 570 },
  71: { name: "Nuh", arabicName: "نوح", startPage: 570, endPage: 572 },
  72: { name: "Al-Jinn", arabicName: "الجن", startPage: 572, endPage: 574 },
  73: { name: "Al-Muzzammil", arabicName: "المزمل", startPage: 574, endPage: 575 },
  74: { name: "Al-Muddaththir", arabicName: "المدثر", startPage: 575, endPage: 577 },
  75: { name: "Al-Qiyamah", arabicName: "القيامة", startPage: 577, endPage: 578 },
  76: { name: "Al-Insan", arabicName: "الإنسان", startPage: 578, endPage: 580 },
  77: { name: "Al-Mursalat", arabicName: "المرسلات", startPage: 580, endPage: 582 },
  78: { name: "An-Naba", arabicName: "النبأ", startPage: 582, endPage: 583 },
  79: { name: "An-Nazi'at", arabicName: "النازعات", startPage: 583, endPage: 584 },
  80: { name: "Abasa", arabicName: "عبس", startPage: 584, endPage: 585 },
  81: { name: "At-Takwir", arabicName: "التكوير", startPage: 585, endPage: 586 },
  82: { name: "Al-Infitar", arabicName: "الانفطار", startPage: 586, endPage: 587 },
  83: { name: "Al-Mutaffifin", arabicName: "المطففين", startPage: 587, endPage: 589 },
  84: { name: "Al-Inshiqaq", arabicName: "الانشقاق", startPage: 589, endPage: 590 },
  85: { name: "Al-Buruj", arabicName: "البروج", startPage: 590, endPage: 591 },
  86: { name: "At-Tariq", arabicName: "الطارق", startPage: 591, endPage: 591 },
  87: { name: "Al-A'la", arabicName: "الأعلى", startPage: 591, endPage: 592 },
  88: { name: "Al-Ghashiyah", arabicName: "الغاشية", startPage: 592, endPage: 592 },
  89: { name: "Al-Fajr", arabicName: "الفجر", startPage: 593, endPage: 594 },
  90: { name: "Al-Balad", arabicName: "البلد", startPage: 594, endPage: 595 },
  91: { name: "Ash-Shams", arabicName: "الشمس", startPage: 595, endPage: 595 },
  92: { name: "Al-Layl", arabicName: "الليل", startPage: 595, endPage: 596 },
  93: { name: "Ad-Duhaa", arabicName: "الضحى", startPage: 596, endPage: 596 },
  94: { name: "Ash-Sharh", arabicName: "الشرح", startPage: 596, endPage: 596 },
  95: { name: "At-Tin", arabicName: "التين", startPage: 597, endPage: 597 },
  96: { name: "Al-Alaq", arabicName: "العلق", startPage: 597, endPage: 597 },
  97: { name: "Al-Qadr", arabicName: "القدر", startPage: 598, endPage: 598 },
  98: { name: "Al-Bayyinah", arabicName: "البينة", startPage: 598, endPage: 599 },
  99: { name: "Az-Zalzalah", arabicName: "الزلزلة", startPage: 599, endPage: 599 },
  100: { name: "Al-Adiyat", arabicName: "العاديات", startPage: 599, endPage: 600 },
  101: { name: "Al-Qari'ah", arabicName: "القارعة", startPage: 600, endPage: 600 },
  102: { name: "At-Takathur", arabicName: "التكاثر", startPage: 600, endPage: 600 },
  103: { name: "Al-Asr", arabicName: "العصر", startPage: 601, endPage: 601 },
  104: { name: "Al-Humazah", arabicName: "الهمزة", startPage: 601, endPage: 601 },
  105: { name: "Al-Fil", arabicName: "الفيل", startPage: 601, endPage: 601 },
  106: { name: "Quraysh", arabicName: "قريش", startPage: 602, endPage: 602 },
  107: { name: "Al-Ma'un", arabicName: "الماعون", startPage: 602, endPage: 602 },
  108: { name: "Al-Kawthar", arabicName: "الكوثر", startPage: 602, endPage: 602 },
  109: { name: "Al-Kafirun", arabicName: "الكافرون", startPage: 603, endPage: 603 },
  110: { name: "An-Nasr", arabicName: "النصر", startPage: 603, endPage: 603 },
  111: { name: "Al-Masad", arabicName: "المسد", startPage: 603, endPage: 603 },
  112: { name: "Al-Ikhlas", arabicName: "الإخلاص", startPage: 604, endPage: 604 },
  113: { name: "Al-Falaq", arabicName: "الفلق", startPage: 604, endPage: 604 },
  114: { name: "An-Nas", arabicName: "الناس", startPage: 604, endPage: 604 }
};

// Helper function to get Surah info by page number
export const getSurahByPage = (pageNumber) => {
  for (const [surahId, surahInfo] of Object.entries(SURAH_PAGE_MAPPING)) {
    if (pageNumber >= surahInfo.startPage && pageNumber <= surahInfo.endPage) {
      return {
        id: parseInt(surahId),
        ...surahInfo
      };
    }
  }
  return null;
};

// Helper function to get all pages for a Surah
export const getPagesForSurah = (surahId) => {
  const surahInfo = SURAH_PAGE_MAPPING[surahId];
  if (!surahInfo) return [];
  
  const pages = [];
  for (let page = surahInfo.startPage; page <= surahInfo.endPage; page++) {
    pages.push(page);
  }
  return pages;
};

// Helper function to get page pairs (right-left) for display
export const getPagePairs = (pages) => {
  const pairs = [];
  for (let i = 0; i < pages.length; i += 2) {
    if (i + 1 < pages.length) {
      // Normal pair: right page (odd), left page (even)
      pairs.push({
        rightPage: pages[i],
        leftPage: pages[i + 1]
      });
    } else {
      // Single page (only right page)
      pairs.push({
        rightPage: pages[i],
        leftPage: null
      });
    }
  }
  return pairs;
};
