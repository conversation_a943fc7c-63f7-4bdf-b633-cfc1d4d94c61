// Simplified mapping of Surahs to their starting pages
// This data maps each Surah to its starting page number

export const SURAH_START_PAGES = {
  1: 1,    // Al-Fatiha
  2: 2,    // Al-Baqarah
  3: 50,   // <PERSON>
  4: 77,   // <PERSON>-<PERSON><PERSON>
  5: 106,  // Al-<PERSON>'idah
  6: 128,  // Al-<PERSON>'am
  7: 151,  // Al-A'raf
  8: 177,  // Al-Anfal
  9: 187,  // At-Tawbah
  10: 208, // <PERSON><PERSON>
  11: 221, // <PERSON><PERSON>
  12: 235, // <PERSON>
  13: 249, // <PERSON><PERSON>-<PERSON><PERSON><PERSON>
  14: 255, // <PERSON>
  15: 262, // Al-Hijr
  16: 267, // An-Nahl
  17: 282, // Al-Isra
  18: 293, // Al-Kahf
  19: 305, // <PERSON><PERSON>
  20: 312, // <PERSON>ha
  21: 322, // Al-Anbya
  22: 332, // Al-Hajj
  23: 342, // <PERSON>-<PERSON>
  24: 350, // An-<PERSON>ur
  25: 359, // Al-Furqan
  26: 367, // Ash-Shu'ara
  27: 377, // An-<PERSON><PERSON>
  28: 385, // Al-Qasa<PERSON>
  29: 396, // <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  30: 404, // Ar-<PERSON><PERSON>
  31: 411, // <PERSON><PERSON><PERSON>
  32: 415, // As-Sajdah
  33: 418, // <PERSON>-<PERSON>zab
  34: 428, // Saba
  35: 434, // <PERSON>ir
  36: 440, // Ya-Sin
  37: 446, // As-Saffat
  38: 453, // Sad
  39: 458, // Az-Zumar
  40: 467, // Ghafir
  41: 477, // Fussilat
  42: 483, // Ash-Shuraa
  43: 489, // Az-Zukhruf
  44: 496, // Ad-Dukhan
  45: 499, // Al-Jathiyah
  46: 502, // Al-Ahqaf
  47: 507, // Muhammad
  48: 511, // Al-Fath
  49: 515, // Al-Hujurat
  50: 518, // Qaf
  51: 520, // Adh-Dhariyat
  52: 523, // At-Tur
  53: 526, // An-Najm
  54: 528, // Al-Qamar
  55: 531, // Ar-Rahman
  56: 534, // Al-Waqi'ah
  57: 537, // Al-Hadid
  58: 542, // Al-Mujadila
  59: 546, // Al-Hashr
  60: 549, // Al-Mumtahanah
  61: 551, // As-Saff
  62: 553, // Al-Jumu'ah
  63: 554, // Al-Munafiqun
  64: 556, // At-Taghabun
  65: 558, // At-Talaq
  66: 560, // At-Tahrim
  67: 562, // Al-Mulk
  68: 564, // Al-Qalam
  69: 566, // Al-Haqqah
  70: 568, // Al-Ma'arij
  71: 570, // Nuh
  72: 572, // Al-Jinn
  73: 574, // Al-Muzzammil
  74: 575, // Al-Muddaththir
  75: 577, // Al-Qiyamah
  76: 578, // Al-Insan
  77: 580, // Al-Mursalat
  78: 582, // An-Naba
  79: 583, // An-Nazi'at
  80: 584, // Abasa
  81: 585, // At-Takwir
  82: 586, // Al-Infitar
  83: 587, // Al-Mutaffifin
  84: 589, // Al-Inshiqaq
  85: 590, // Al-Buruj
  86: 591, // At-Tariq
  87: 591, // Al-A'la
  88: 592, // Al-Ghashiyah
  89: 593, // Al-Fajr
  90: 594, // Al-Balad
  91: 595, // Ash-Shams
  92: 595, // Al-Layl
  93: 596, // Ad-Duhaa
  94: 596, // Ash-Sharh
  95: 597, // At-Tin
  96: 597, // Al-Alaq
  97: 598, // Al-Qadr
  98: 598, // Al-Bayyinah
  99: 599, // Az-Zalzalah
  100: 599, // Al-Adiyat
  101: 600, // Al-Qari'ah
  102: 600, // At-Takathur
  103: 601, // Al-Asr
  104: 601, // Al-Humazah
  105: 601, // Al-Fil
  106: 602, // Quraysh
  107: 602, // Al-Ma'un
  108: 602, // Al-Kawthar
  109: 603, // Al-Kafirun
  110: 603, // An-Nasr
  111: 603, // Al-Masad
  112: 604, // Al-Ikhlas
  113: 604, // Al-Falaq
  114: 604  // An-Nas
};

// Helper function to get the starting page for a Surah
export const getSurahStartPage = (surahNumber) => {
  return SURAH_START_PAGES[surahNumber] || 1;
};

// Helper function to get the odd page number (right page in RTL reading)
export const getOddPageNumber = (pageNumber) => {
  // If page is even, return the previous odd page
  // If page is odd, return the page itself
  return pageNumber % 2 === 0 ? pageNumber - 1 : pageNumber;
};
