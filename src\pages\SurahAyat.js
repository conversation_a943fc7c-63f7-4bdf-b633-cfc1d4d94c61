import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaChevronLeft, FaChevronRight, FaArrowLeft } from 'react-icons/fa';

function SurahAyat() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [surah, setSurah] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const AYAHS_PER_PAGE = 5; // Number of ayahs to show per page

  useEffect(() => {
    async function fetchSurah() {
      try {
        setLoading(true);
        setError(null);
        console.log(`Fetching surah ${id} data from Alquran.cloud API...`);

        // Using the Alquran.cloud API which is more reliable and doesn't require authentication
        // Fetching both Arabic text and English translation
        const arabicResponse = await fetch(`https://api.alquran.cloud/v1/surah/${id}`);

        if (!arabicResponse.ok) {
          throw new Error(`API returned status code ${arabicResponse.status}`);
        }

        const arabicData = await arabicResponse.json();
        console.log('Arabic surah data:', arabicData);

        if (!arabicData || !arabicData.data || arabicData.code !== 200) {
          throw new Error('Invalid data format received from API for Arabic text');
        }

        // Fetch English translation
        const englishResponse = await fetch(`https://api.alquran.cloud/v1/surah/${id}/en.asad`);

        if (!englishResponse.ok) {
          throw new Error(`API returned status code ${englishResponse.status} for English translation`);
        }

        const englishData = await englishResponse.json();
        console.log('English surah data:', englishData);

        if (!englishData || !englishData.data || englishData.code !== 200) {
          throw new Error('Invalid data format received from API for English translation');
        }

        // Combine the Arabic and English data
        const combinedSurah = {
          number: arabicData.data.number,
          englishName: arabicData.data.englishName,
          arabicName: arabicData.data.name,
          ayahs: arabicData.data.ayahs.map((ayah, index) => ({
            number: ayah.number,
            arabicText: ayah.text,
            englishText: englishData.data.ayahs[index].text
          }))
        };

        console.log('Combined surah data:', combinedSurah);
        setSurah(combinedSurah);
      } catch (error) {
        console.error('Error fetching surah:', error);
        setError(`Failed to fetch Quran data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchSurah();
  }, [id]);

  if (loading) return <p style={{ marginTop: '100px' }}>Loading surah...</p>;

  if (error) return <p style={{ marginTop: '100px', color: 'red' }}>{error}</p>;

  if (!surah) return <p style={{ marginTop: '100px' }}>No data found for this surah.</p>;

  // Calculate total pages
  const totalPages = Math.ceil(surah.ayahs.length / AYAHS_PER_PAGE);

  // Get current ayahs for the page
  const startIndex = (currentPage - 1) * AYAHS_PER_PAGE;
  const endIndex = Math.min(startIndex + AYAHS_PER_PAGE, surah.ayahs.length);
  const currentAyahs = surah.ayahs.slice(startIndex, endIndex);

  // Handle page navigation
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      window.scrollTo(0, 0);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
      window.scrollTo(0, 0);
    }
  };

  // Handle surah navigation
  const goToPreviousSurah = () => {
    if (parseInt(id) > 1) {
      navigate(`/surahayat/${parseInt(id) - 1}`);
      setCurrentPage(1);
    }
  };

  const goToNextSurah = () => {
    navigate(`/surahayat/${parseInt(id) + 1}`);
    setCurrentPage(1);
  };

  return (
    <div style={{
      marginTop: '120px',
      padding: '20px',
      maxWidth: '1000px',
      margin: '0 auto',
      position: 'relative'
    }}><div style={{ marginTop: "30px", textAlign: "center" }}>
    <h3 style={{ color: '#8B4513' }}>Quran Page Preview</h3>
    <img
      src={`https://cdn.islamic.network/quran/pages/1.png`}
      alt="Quran Page"
      style={{ maxWidth: '100%', border: '2px solid #d4af37', boxShadow: '0 0 20px rgba(0,0,0,0.1)' }}
    />
  </div>
  
      {/* Back to Surahs Button */}
      <div style={{
        position: 'absolute',
        top: '80px',
        left: '24px',
      }}>
        <button
          onClick={() => navigate('/surahs')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            background: '#8B4513',
            color: 'white',
            border: 'none',
            padding: '10px 15px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
          }}
        >
          <FaArrowLeft /> Back to Surahs
        </button>
      </div>

      {/* Quran Book Header */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
        borderBottom: '2px solid #d4af37',
        paddingBottom: '15px'
      }}>
        <h1 style={{
          color: '#d4af37',
          marginBottom: '10px',
          fontFamily: 'serif'
        }}>
          {surah.englishName}
        </h1>
        <h2 style={{
          color: '#8B4513',
          fontFamily: 'serif',
          fontSize: '32px'
        }}>
          {surah.arabicName}
        </h2>
        <p style={{
          color: '#8B4513',
          fontStyle: 'italic'
        }}>
          Page {currentPage} of {totalPages} • Ayahs {startIndex + 1}-{endIndex} of {surah.ayahs.length}
        </p>
      </div>

      {/* Quran Book Pages with Side Navigation */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        minHeight: '600px',
      }}>
        {/* Left Navigation Button (Next Page for RTL) */}
        <button
          onClick={goToNextPage}
          disabled={currentPage >= totalPages}
          style={{
            background: 'transparent',
            color: currentPage >= totalPages ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: currentPage >= totalPages ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            left: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronLeft />
        </button>

        {/* Quran Content */}
        <div style={{
          background: '#FFF8E1',
          border: '1px solid #d4af37',
          borderRadius: '5px',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
          padding: '40px',
          position: 'relative',
          width: '100%',
          minHeight: '600px',
          backgroundImage: 'linear-gradient(to right, rgba(212, 175, 55, 0.05) 1px, transparent 1px)',
          backgroundSize: '25px 100%'
        }}>
          {/* Decorative Quran Page Border */}
          <div style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            right: '10px',
            bottom: '10px',
            border: '2px solid rgba(139, 69, 19, 0.2)',
            pointerEvents: 'none'
          }}></div>

          {/* Bismillah at the top of each page except for Surah 9 */}
          {surah.number !== 9 && currentPage === 1 && (
            <div style={{
              textAlign: 'center',
              marginBottom: '30px',
              fontFamily: 'serif',
              fontSize: '24px',
              color: '#8B4513'
            }}>
              بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
            </div>
          )}

          {/* Ayahs */}
          <div style={{ direction: 'rtl' }}>
            {currentAyahs.map((ayah, index) => (
              <div key={index} style={{
                marginBottom: '25px',
                position: 'relative',
                paddingBottom: '15px'
              }}>
                {/* Arabic Text */}
                <p style={{
                  textAlign: 'right',
                  fontSize: '26px',
                  lineHeight: '2',
                  fontFamily: 'serif',
                  color: '#000',
                  marginBottom: '15px'
                }}>
                  {ayah.arabicText}
                  <span style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '35px',
                    height: '35px',
                    borderRadius: '50%',
                    background: '#d4af37',
                    color: 'white',
                    fontSize: '14px',
                    marginRight: '10px'
                  }}>
                    {startIndex + index + 1}
                  </span>
                </p>

                {/* English Translation */}
                <p style={{
                  fontSize: '16px',
                  color: '#555',
                  fontFamily: 'serif',
                  lineHeight: '1.6',
                  direction: 'ltr',
                  textAlign: 'left',
                  borderTop: '1px solid rgba(139, 69, 19, 0.2)',
                  paddingTop: '10px'
                }}>
                  {ayah.englishText}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Right Navigation Button (Previous Page for RTL) */}
        <button
          onClick={goToPreviousPage}
          disabled={currentPage <= 1}
          style={{
            background: 'transparent',
            color: currentPage <= 1 ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: currentPage <= 1 ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            right: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronRight />
        </button>
      </div>

      {/* Surah Navigation */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        marginTop: '30px',
        alignItems: 'center'
      }}>
        {/* Next Surah Button (Left side for RTL) */}
        <button
          onClick={goToNextSurah}
          style={{
            background: '#8B4513',
            color: 'white',
            border: 'none',
            padding: '10px 15px',
            borderRadius: '5px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '5px'
          }}
        >
          <FaChevronLeft /> Next Surah
        </button>

        {/* Page Information */}
        <div style={{
          color: '#8B4513',
          fontStyle: 'italic',
          fontSize: '14px'
        }}>
          Page {currentPage} of {totalPages} • Ayahs {startIndex + 1}-{endIndex} of {surah.ayahs.length}
        </div>

        {/* Previous Surah Button (Right side for RTL) */}
        <button
          onClick={goToPreviousSurah}
          disabled={id <= 1}
          style={{
            background: id <= 1 ? '#ccc' : '#8B4513',
            color: 'white',
            border: 'none',
            padding: '10px 15px',
            borderRadius: '5px',
            cursor: id <= 1 ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '5px'
          }}
        >
          Previous Surah <FaChevronRight />
        </button>
      </div>

      {/* Back to Surahs Button (Bottom) */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '30px'
      }}>
        <button
          onClick={() => navigate('/surahs')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            background: '#d4af37',
            color: 'white',
            border: 'none',
            padding: '12px 20px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
          }}
        >
          <FaArrowLeft /> Back to All Surahs
        </button>
      </div>
    </div>
  );
}

export default SurahAyat;
