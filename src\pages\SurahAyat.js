import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaChevronLeft, FaChevronRight, FaArrowLeft } from 'react-icons/fa';

function SurahAyat() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [surah, setSurah] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);

  // Mapping of surah numbers to their starting pages
  const surahStartingPages = {
    1: 1,    // Al-Fatiha
    2: 1,    // Al-Baqarah
    3: 49,   // Al-<PERSON><PERSON>ran
    4: 77,   // An-<PERSON>sa
    5: 105,  // Al-Ma'idah
    6: 127,  // Al-An'am
    7: 151,  // Al-A'raf
    8: 177,  // Al-Anfal
    9: 187,  // At-Tawbah
    10: 207, // <PERSON><PERSON>
    11: 221, // Hud
    12: 235, // Yusuf
    13: 249, // Ar-<PERSON>'d
    14: 255, // <PERSON>
    15: 261, // Al-Hijr
    16: 267, // An-Nahl
    17: 281, // <PERSON>-Isra
    18: 293, // Al-Kahf
    19: 305, // Maryam
    20: 311, // Ta-Ha
    21: 321, // Al-Anbiya
    22: 331, // Al-Hajj
    23: 341, // Al-Mu'minun
    24: 349, // An-Nur
    25: 359, // Al-Furqan
    26: 367, // Ash-Shu'ara
    27: 377, // An-Naml
    28: 385, // Al-Qasas
    29: 395, // Al-Ankabut
    30: 403, // Ar-Rum
    31: 411, // Luqman
    32: 415, // As-Sajdah
    33: 417, // Al-Ahzab
    34: 427, // Saba
    35: 433, // Fatir
    36: 439, // Ya-Sin
    37: 445, // As-Saffat
    38: 453, // Sad
    39: 457, // Az-Zumar
    40: 467, // Ghafir
    41: 477, // Fussilat
    42: 483, // Ash-Shura
    43: 489, // Az-Zukhruf
    44: 495, // Ad-Dukhan
    45: 499, // Al-Jathiyah
    46: 501, // Al-Ahqaf
    47: 507, // Muhammad
    48: 511, // Al-Fath
    49: 515, // Al-Hujurat
    50: 517, // Qaf
    51: 519, // Adh-Dhariyat
    52: 523, // At-Tur
    53: 525, // An-Najm
    54: 527, // Al-Qamar
    55: 531, // Ar-Rahman
    56: 533, // Al-Waqi'ah
    57: 537, // Al-Hadid
    58: 541, // Al-Mujadilah
    59: 545, // Al-Hashr
    60: 549, // Al-Mumtahanah
    61: 551, // As-Saf
    62: 553, // Al-Jumu'ah
    63: 553, // Al-Munafiqun
    64: 555, // At-Taghabun
    65: 557, // At-Talaq
    66: 559, // At-Tahrim
    67: 561, // Al-Mulk
    68: 563, // Al-Qalam
    69: 565, // Al-Haqqah
    70: 567, // Al-Ma'arij
    71: 569, // Nuh
    72: 571, // Al-Jinn
    73: 573, // Al-Muzzammil
    74: 575, // Al-Muddathir
    75: 577, // Al-Qiyamah
    76: 577, // Al-Insan
    77: 579, // Al-Mursalat
    78: 581, // An-Naba
    79: 583, // An-Nazi'at
    80: 585, // Abasa
    81: 585, // At-Takwir
    82: 587, // Al-Infitar
    83: 587, // Al-Mutaffifin
    84: 589, // Al-Inshiqaq
    85: 589, // Al-Buruj
    86: 591, // At-Tariq
    87: 591, // Al-A'la
    88: 591, // Al-Ghashiyah
    89: 593, // Al-Fajr
    90: 593, // Al-Balad
    91: 595, // Ash-Shams
    92: 595, // Al-Lail
    93: 595, // Ad-Duha
    94: 595, // Al-Inshirah
    95: 597, // At-Tin
    96: 597, // Al-Alaq
    97: 597, // Al-Qadr
    98: 597, // Al-Bayyinah
    99: 599, // Az-Zalzalah
    100: 599, // Al-Adiyat
    101: 599, // Al-Qari'ah
    102: 599, // At-Takathur
    103: 601, // Al-Asr
    104: 601, // Al-Humazah
    105: 601, // Al-Fil
    106: 601, // Quraysh
    107: 601, // Al-Ma'un
    108: 601, // Al-Kawthar
    109: 603, // Al-Kafirun
    110: 603, // An-Nasr
    111: 603, // Al-Masad
    112: 603, // Al-Ikhlas
    113: 603, // Al-Falaq
    114: 603  // An-Nas
  };

  useEffect(() => {
    async function fetchSurah() {
      try {
        setLoading(true);
        setError(null);
        console.log(`Fetching surah ${id} data from Alquran.cloud API...`);

        const response = await fetch(`https://api.alquran.cloud/v1/surah/${id}`);

        if (!response.ok) {
          throw new Error(`API returned status code ${response.status}`);
        }

        const data = await response.json();
        console.log('Surah data:', data);

        if (!data || !data.data || data.code !== 200) {
          throw new Error('Invalid data format received from API');
        }

        setSurah(data.data);
        
        // Set the starting page based on the surah number
        const startingPage = surahStartingPages[parseInt(id)] || 1;
        setCurrentPage(startingPage);
      } catch (error) {
        console.error('Error fetching surah:', error);
        setError(`Failed to fetch Quran data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchSurah();
  }, [id]);

  // Handle page navigation
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 2); // Go back two pages
      window.scrollTo(0, 0);
    }
  };

  const goToNextPage = () => {
    if (currentPage < 603) { // 603 is the last even-numbered page
      setCurrentPage(currentPage + 2); // Go forward two pages
      window.scrollTo(0, 0);
    }
  };

  // Function to get the correct page number for the image
  const getImagePageNumber = (pageNum) => {
    return pageNum.toString().padStart(3, '0');
  };

  if (loading) return <p style={{ marginTop: '100px' }}>Loading surah...</p>;
  if (error) return <p style={{ marginTop: '100px', color: 'red' }}>{error}</p>;
  if (!surah) return <p style={{ marginTop: '100px' }}>No data found for this surah.</p>;

  return (
    <div style={{
      marginTop: '120px',
      padding: '20px',
      maxWidth: '1200px',
      margin: '0 auto',
      position: 'relative'
    }}>
      {/* Back to Surahs Button */}
      <div style={{
        position: 'absolute',
        top: '80px',
        left: '24px',
      }}>
        <button
          onClick={() => navigate('/surahs')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            background: '#8B4513',
            color: 'white',
            border: 'none',
            padding: '10px 15px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            transition: 'all 0.3s ease'
          }}
        >
          <FaArrowLeft /> Back to Surahs
        </button>
      </div>

      {/* Quran Book Header */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
        borderBottom: '2px solid #d4af37',
        paddingBottom: '15px'
      }}>
        <h1 style={{
          color: '#d4af37',
          marginBottom: '10px',
          fontFamily: 'serif'
        }}>
          {surah.englishName}
        </h1>
        <h2 style={{
          color: '#8B4513',
          fontFamily: 'serif',
          fontSize: '32px'
        }}>
          {surah.name}
        </h2>
        <p style={{
          color: '#8B4513',
          fontStyle: 'italic'
        }}>
          Pages {currentPage}-{currentPage + 1}
        </p>
      </div>

      {/* Quran Pages Container */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        minHeight: '600px',
        gap: '-10px',
        maxWidth: '1200px',
        margin: '0 auto',
        direction: 'rtl'
      }}>
        {/* Right Navigation Button */}
        <button
          onClick={goToPreviousPage}
          disabled={currentPage <= 1}
          style={{
            background: 'transparent',
            color: currentPage <= 1 ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: currentPage <= 1 ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            right: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronRight />
        </button>

        {/* Right Page (First Page) */}
        <div style={{
          background: '#FFF8E1',
          border: '1px solid #d4af37',
          borderLeft: 'none',
          borderRadius: '0 5px 5px 0',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
          padding: '20px',
          position: 'relative',
          width: '50%',
          minHeight: '600px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          {loading ? (
            <div style={{
              textAlign: 'center',
              color: '#8B4513',
              fontSize: '18px'
            }}>
              Loading Quran page...
            </div>
          ) : (
            <img
              src={`/images/quran-pages/${getImagePageNumber(currentPage)}.png`}
              alt={`Quran Page ${currentPage}`}
              style={{
                maxWidth: '100%',
                height: 'auto',
                maxHeight: '550px',
                boxShadow: '0 5px 15px rgba(0,0,0,0.2)'
              }}
              onError={(e) => {
                console.error('Failed to load Quran page image');
                e.target.onerror = null;
                const container = e.target.parentElement;
                const errorMessage = document.createElement('div');
                errorMessage.style.cssText = `
                  text-align: center;
                  color: #8B4513;
                  padding: 20px;
                  font-size: 18px;
                  background: #FFF8E1;
                  border: 1px solid #d4af37;
                  border-radius: 5px;
                `;
                errorMessage.textContent = 'Unable to load Quran page image. Please check if the image exists in the correct format.';
                container.appendChild(errorMessage);
              }}
            />
          )}
        </div>

        {/* Left Page (Second Page) */}
        <div style={{
          background: '#FFF8E1',
          border: '1px solid #d4af37',
          borderRight: 'none',
          borderRadius: '5px 0 0 5px',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
          padding: '20px',
          position: 'relative',
          width: '50%',
          minHeight: '600px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          {loading ? (
            <div style={{
              textAlign: 'center',
              color: '#8B4513',
              fontSize: '18px'
            }}>
              Loading Quran page...
            </div>
          ) : (
            <img
              src={`/images/quran-pages/${getImagePageNumber(currentPage + 1)}.png`}
              alt={`Quran Page ${currentPage + 1}`}
              style={{
                maxWidth: '100%',
                height: 'auto',
                maxHeight: '550px',
                boxShadow: '0 5px 15px rgba(0,0,0,0.2)'
              }}
              onError={(e) => {
                console.error('Failed to load Quran page image');
                e.target.onerror = null;
                const container = e.target.parentElement;
                const errorMessage = document.createElement('div');
                errorMessage.style.cssText = `
                  text-align: center;
                  color: #8B4513;
                  padding: 20px;
                  font-size: 18px;
                  background: #FFF8E1;
                  border: 1px solid #d4af37;
                  border-radius: 5px;
                `;
                errorMessage.textContent = 'Unable to load Quran page image. Please check if the image exists in the correct format.';
                container.appendChild(errorMessage);
              }}
            />
          )}
        </div>

        {/* Left Navigation Button */}
        <button
          onClick={goToNextPage}
          disabled={currentPage >= 603}
          style={{
            background: 'transparent',
            color: currentPage >= 603 ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: currentPage >= 603 ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            left: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronLeft />
        </button>
      </div>
    </div>
  );
}

export default SurahAyat;
