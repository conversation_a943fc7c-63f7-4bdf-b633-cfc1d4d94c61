import { useState, useRef } from 'react';
import { <PERSON>a<PERSON><PERSON>, FaUser } from 'react-icons/fa';
import { uploadUserImage, getUserImageUrl, updateStoredUserData } from '../utils/userApi';

function UserProfileImage({ 
  user, 
  onImageUpdate, 
  size = 80, 
  showUploadButton = true,
  className = '' 
}) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const fileInputRef = useRef(null);

  const handleImageClick = () => {
    if (showUploadButton && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setUploadError('Please select a valid image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setUploadError('Image size should be less than 5MB');
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      const result = await uploadUserImage(user.id, file);
      
      // Update user data with new image
      const updatedUser = {
        ...user,
        image: result.imagePath || result.image || file.name
      };
      
      // Update localStorage
      updateStoredUserData(updatedUser);
      
      // Notify parent component
      if (onImageUpdate) {
        onImageUpdate(updatedUser);
      }

      console.log('Image uploaded successfully:', result);
    } catch (error) {
      console.error('Error uploading image:', error);
      setUploadError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const getImageSrc = () => {
    if (user?.image) {
      const imageUrl = getUserImageUrl(user.id, user.image);
      if (imageUrl) return imageUrl;
    }
    return null;
  };

  const imageSrc = getImageSrc();

  return (
    <div 
      className={className}
      style={{
        position: 'relative',
        display: 'inline-block',
        cursor: showUploadButton ? 'pointer' : 'default'
      }}
    >
      {/* Profile Image or Default Icon */}
      <div
        onClick={handleImageClick}
        style={{
          width: size,
          height: size,
          borderRadius: '50%',
          backgroundColor: '#f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          border: '3px solid #d4af37',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease',
          position: 'relative'
        }}
        onMouseEnter={(e) => {
          if (showUploadButton) {
            e.target.style.transform = 'scale(1.05)';
            e.target.style.boxShadow = '0 6px 20px rgba(0,0,0,0.2)';
          }
        }}
        onMouseLeave={(e) => {
          if (showUploadButton) {
            e.target.style.transform = 'scale(1)';
            e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
          }
        }}
      >
        {imageSrc ? (
          <img
            src={imageSrc}
            alt="Profile"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
        ) : null}
        
        {/* Default Icon (shown when no image or image fails to load) */}
        <div
          style={{
            display: imageSrc ? 'none' : 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
            color: '#bfa131'
          }}
        >
          <FaUser size={size * 0.4} />
        </div>

        {/* Loading Overlay */}
        {isUploading && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0,0,0,0.7)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '12px'
            }}
          >
            Uploading...
          </div>
        )}
      </div>

      {/* Upload Button */}
      {showUploadButton && !isUploading && (
        <button
          onClick={handleImageClick}
          style={{
            position: 'absolute',
            bottom: -2,
            right: -2,
            width: size * 0.3,
            height: size * 0.3,
            borderRadius: '50%',
            backgroundColor: '#d4af37',
            border: '2px solid white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = '#bfa131';
            e.target.style.transform = 'scale(1.1)';
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = '#d4af37';
            e.target.style.transform = 'scale(1)';
          }}
          title="Upload new profile image"
        >
          <FaPlus size={size * 0.12} color="white" />
        </button>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />

      {/* Error Message */}
      {uploadError && (
        <div
          style={{
            position: 'absolute',
            top: '100%',
            left: '50%',
            transform: 'translateX(-50%)',
            marginTop: '8px',
            padding: '4px 8px',
            backgroundColor: '#ff4444',
            color: 'white',
            borderRadius: '4px',
            fontSize: '12px',
            whiteSpace: 'nowrap',
            zIndex: 1000
          }}
        >
          {uploadError}
        </div>
      )}
    </div>
  );
}

export default UserProfileImage;
