// Utility functions for Quran reading plan management

const TOTAL_QURAN_PAGES = 604;

// Get current date in Egypt/Cairo timezone
export const getCurrentDateInCairo = () => {
  const now = new Date();
  // Convert to Cairo timezone (UTC+2 or UTC+3 depending on DST)
  const cairoTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
  return cairoTime;
};

// Get date string in YYYY-MM-DD format for Cairo timezone
export const getCairoDateString = (date = null) => {
  const targetDate = date || getCurrentDateInCairo();
  const cairoDate = new Date(targetDate.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
  return cairoDate.toISOString().split('T')[0];
};

// Calculate pages per day based on duration
export const calculatePagesPerDay = (duration, durationType) => {
  const totalDays = durationType === 'months' ? duration * 30 : duration;
  return Math.ceil(TOTAL_QURAN_PAGES / totalDays);
};

// Calculate total days for the plan
export const calculateTotalDays = (duration, durationType) => {
  return durationType === 'months' ? duration * 30 : duration;
};

// Get current day's reading task
export const getCurrentReadingTask = (planData) => {
  if (!planData || !planData.startDate) return null;

  const startDate = new Date(planData.startDate);
  const currentDate = getCurrentDateInCairo();
  const daysPassed = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
  
  // If plan hasn't started yet
  if (daysPassed < 0) return null;
  
  // If plan is completed
  if (daysPassed >= planData.totalDays) return null;

  // Calculate current page range
  const pagesPerDay = planData.pagesPerDay;
  const startPage = TOTAL_QURAN_PAGES - (daysPassed * pagesPerDay);
  const endPage = Math.max(1, startPage - pagesPerDay + 1);

  // Handle missed days by combining tasks
  const missedDays = getMissedDays(planData, daysPassed);
  const totalMissedPages = missedDays.length * pagesPerDay;
  const adjustedStartPage = startPage + totalMissedPages;
  const adjustedEndPage = Math.max(1, endPage - totalMissedPages);

  return {
    dayNumber: daysPassed + 1,
    startPage: Math.min(TOTAL_QURAN_PAGES, adjustedStartPage),
    endPage: Math.max(1, adjustedEndPage),
    pagesCount: Math.min(TOTAL_QURAN_PAGES, adjustedStartPage) - Math.max(1, adjustedEndPage) + 1,
    hasMissedDays: missedDays.length > 0,
    missedDaysCount: missedDays.length
  };
};

// Get missed days
export const getMissedDays = (planData, currentDay) => {
  if (!planData || !planData.completedDays) return [];
  
  const missedDays = [];
  for (let i = 0; i < currentDay; i++) {
    if (!planData.completedDays[i]) {
      missedDays.push(i);
    }
  }
  return missedDays;
};

// Mark day as completed
export const markDayCompleted = (planData, dayNumber) => {
  const updatedPlan = { ...planData };
  if (!updatedPlan.completedDays) {
    updatedPlan.completedDays = {};
  }
  updatedPlan.completedDays[dayNumber - 1] = true;
  updatedPlan.lastCompletedDate = getCurrentDateInCairo().toISOString();
  
  // Update completion percentage
  const completedCount = Object.values(updatedPlan.completedDays).filter(Boolean).length;
  updatedPlan.completionPercentage = Math.round((completedCount / updatedPlan.totalDays) * 100);
  
  return updatedPlan;
};

// Calculate completion percentage
export const calculateCompletionPercentage = (planData) => {
  if (!planData || !planData.completedDays) return 0;
  
  const completedCount = Object.values(planData.completedDays).filter(Boolean).length;
  return Math.round((completedCount / planData.totalDays) * 100);
};

// Get plan status for calendar
export const getPlanCalendarData = (planData) => {
  if (!planData) return [];
  
  const startDate = new Date(planData.startDate);
  const calendarData = [];
  
  for (let i = 0; i < planData.totalDays; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    
    let status = 'planned'; // Default status
    const currentDate = getCurrentDateInCairo();
    const dayDate = new Date(date);
    dayDate.setHours(0, 0, 0, 0);
    currentDate.setHours(0, 0, 0, 0);
    
    if (dayDate < currentDate) {
      // Past day
      status = planData.completedDays && planData.completedDays[i] ? 'completed' : 'missed';
    } else if (dayDate.getTime() === currentDate.getTime()) {
      // Today
      status = planData.completedDays && planData.completedDays[i] ? 'completed' : 'current';
    }
    
    calendarData.push({
      date: date.toISOString().split('T')[0],
      dayNumber: i + 1,
      status: status,
      startPage: TOTAL_QURAN_PAGES - (i * planData.pagesPerDay),
      endPage: Math.max(1, TOTAL_QURAN_PAGES - ((i + 1) * planData.pagesPerDay) + 1)
    });
  }
  
  return calendarData;
};

// Create new reading plan
export const createReadingPlan = (duration, durationType) => {
  const totalDays = calculateTotalDays(duration, durationType);
  const pagesPerDay = calculatePagesPerDay(duration, durationType);
  
  return {
    duration,
    durationType,
    totalDays,
    pagesPerDay,
    startDate: getCurrentDateInCairo().toISOString(),
    completedDays: {},
    completionPercentage: 0,
    createdAt: getCurrentDateInCairo().toISOString()
  };
};

// Load plan from localStorage
export const loadReadingPlan = () => {
  try {
    const planData = localStorage.getItem('quranReadingPlan');
    return planData ? JSON.parse(planData) : null;
  } catch (error) {
    console.error('Error loading reading plan:', error);
    return null;
  }
};

// Save plan to localStorage
export const saveReadingPlan = (planData) => {
  try {
    localStorage.setItem('quranReadingPlan', JSON.stringify(planData));
    return true;
  } catch (error) {
    console.error('Error saving reading plan:', error);
    return false;
  }
};

// Check if plan is active
export const isPlanActive = (planData) => {
  if (!planData || !planData.startDate) return false;

  const startDate = new Date(planData.startDate);
  const currentDate = getCurrentDateInCairo();
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + planData.totalDays);

  return currentDate >= startDate && currentDate <= endDate;
};

// Delete/cancel reading plan
export const cancelReadingPlan = () => {
  try {
    localStorage.removeItem('quranReadingPlan');
    return true;
  } catch (error) {
    console.error('Error canceling reading plan:', error);
    return false;
  }
};
