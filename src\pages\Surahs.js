import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { SURAH_PAGE_MAPPING, getPagesForSurah, getPagePairs, getSurahByPage } from '../utils/surahPageMapping';
import { API_ENDPOINTS } from '../config/apiConfig';
import { getStoredUserData } from '../utils/userApi';

function Surahs() {
  const navigate = useNavigate();
  const [currentPairIndex, setCurrentPairIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get all Quran pages (1-604) and create pairs
  const allPages = Array.from({ length: 604 }, (_, i) => i + 1);
  const pagePairs = getPagePairs(allPages);
  const currentPair = pagePairs[currentPairIndex];

  // Function to update streak when page is accessed
  const updateStreakForPage = async (pageNumber) => {
    try {
      const userData = getStoredUserData();
      if (!userData || !userData.id) return;

      const surahInfo = getSurahByPage(pageNumber);
      if (!surahInfo) return;

      const response = await fetch(API_ENDPOINTS.UPDATE_STREAK(userData.id), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lastSurah: surahInfo.id,
          lastAyah: pageNumber
        }),
      });

      if (response.ok) {
        console.log('✅ Streak updated for page:', pageNumber);
      }
    } catch (error) {
      console.error('❌ Error updating streak for page:', error);
    }
  };

  // Navigation functions
  const handlePrevPair = () => {
    if (currentPairIndex > 0) {
      const newIndex = currentPairIndex - 1;
      setCurrentPairIndex(newIndex);
      const newPair = pagePairs[newIndex];
      updateStreakForPage(newPair.rightPage);
    }
  };

  const handleNextPair = () => {
    if (currentPairIndex < pagePairs.length - 1) {
      const newIndex = currentPairIndex + 1;
      setCurrentPairIndex(newIndex);
      const newPair = pagePairs[newIndex];
      updateStreakForPage(newPair.rightPage);
    }
  };

  // Get current Surah name based on the right page
  const getCurrentSurahName = () => {
    if (!currentPair) return 'Al-Fatiha';

    const rightPageSurah = getSurahByPage(currentPair.rightPage);
    return rightPageSurah?.name || 'Al-Fatiha';
  };

  // Handle page click to navigate to specific Surah
  const handlePageClick = (pageNumber) => {
    const surahInfo = getSurahByPage(pageNumber);
    if (surahInfo) {
      updateStreakForPage(pageNumber);
      navigate(`/surahayat/${surahInfo.id}?page=${pageNumber}`);
    }
  };

  if (loading) return <p style={{ marginTop: '100px' }}>Loading...</p>;
  if (error) return <p style={{ marginTop: '100px', color: 'red' }}>{error}</p>;
  if (!currentPair) return <p style={{ marginTop: '100px' }}>No pages available.</p>;

  return (
    <div style={{
      marginTop: '120px',
      padding: '20px',
      maxWidth: '1200px',
      margin: '0 auto',
      position: 'relative'
    }}>
      {/* Surah Header */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
        borderBottom: '2px solid #d4af37',
        paddingBottom: '15px'
      }}>
        <h1 style={{
          color: '#d4af37',
          marginBottom: '10px',
          fontFamily: 'serif'
        }}>
          {getCurrentSurahName()}
        </h1>
        <h2 style={{
          color: '#8B4513',
          fontFamily: 'serif',
          fontSize: '32px'
        }}>
          {getSurahByPage(currentPair.rightPage)?.arabicName || 'الفاتحة'}
        </h2>
      </div>

      {/* Quran Pages with Side Navigation */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        minHeight: '800px',
        gap: '20px'
      }}>
        {/* Left Navigation Button (Next Page for RTL) */}
        <button
          onClick={handleNextPair}
          disabled={currentPairIndex >= pagePairs.length - 1}
          style={{
            background: 'transparent',
            color: (currentPairIndex >= pagePairs.length - 1) ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: (currentPairIndex >= pagePairs.length - 1) ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            left: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronLeft />
        </button>

        {/* Quran Page Images */}
        <div style={{
          display: 'flex',
          gap: '20px',
          width: '100%',
          justifyContent: 'center',
          alignItems: 'flex-start'
        }}>
          {/* Right Page (Always displayed, odd page numbers) */}
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}>
            <img
              src={`https://cdn.islamic.network/quran/pages/${currentPair.rightPage}.png`}
              alt={`Quran Page ${currentPair.rightPage}`}
              onClick={() => handlePageClick(currentPair.rightPage)}
              style={{
                maxWidth: '400px',
                width: '100%',
                border: '2px solid #d4af37',
                borderRadius: '8px',
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                cursor: 'pointer',
                transition: 'transform 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'scale(1.02)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'scale(1)';
              }}
            />
            <p style={{
              marginTop: '10px',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#8B4513',
              textAlign: 'center'
            }}>
              Page {currentPair.rightPage}
            </p>
          </div>

          {/* Left Page (Only if exists, even page numbers) */}
          {currentPair.leftPage && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}>
              <img
                src={`https://cdn.islamic.network/quran/pages/${currentPair.leftPage}.png`}
                alt={`Quran Page ${currentPair.leftPage}`}
                onClick={() => handlePageClick(currentPair.leftPage)}
                style={{
                  maxWidth: '400px',
                  width: '100%',
                  border: '2px solid #d4af37',
                  borderRadius: '8px',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'scale(1.02)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'scale(1)';
                }}
              />
              <p style={{
                marginTop: '10px',
                fontSize: '16px',
                fontWeight: 'bold',
                color: '#8B4513',
                textAlign: 'center'
              }}>
                Page {currentPair.leftPage}
              </p>
            </div>
          )}
        </div>

        {/* Right Navigation Button (Previous Page for RTL) */}
        <button
          onClick={handlePrevPair}
          disabled={currentPairIndex <= 0}
          style={{
            background: 'transparent',
            color: (currentPairIndex <= 0) ? '#ccc' : '#8B4513',
            border: 'none',
            fontSize: '24px',
            cursor: (currentPairIndex <= 0) ? 'not-allowed' : 'pointer',
            padding: '10px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
            position: 'absolute',
            right: '-20px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
            backgroundColor: 'white'
          }}
        >
          <FaChevronRight />
        </button>
      </div>

      {/* Page Information */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '20px',
        alignItems: 'center'
      }}>
        <div style={{
          color: '#8B4513',
          fontStyle: 'italic',
          fontSize: '16px',
          textAlign: 'center'
        }}>
          {getCurrentSurahName()} • Page {currentPair.rightPage}{currentPair.leftPage ? ` - ${currentPair.leftPage}` : ''} •
          Pair {currentPairIndex + 1} of {pagePairs.length}
        </div>
      </div>
    </div>
  );
}

export default Surahs;
