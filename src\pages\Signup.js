import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { motion } from 'framer-motion';
import BgImage from '../assets/bg.jpg';
import Lantern from '../assets/lantern.png';

function Signup({ setIsAuthenticated }) {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const navigate = useNavigate();




const handleSubmit = async (e) => {
  e.preventDefault();

  if (password !== confirmPassword) {
    alert("Passwords do not match!");
    return;
  }

  try {
    const response = await fetch('https://kotaby.duckdns.org/users/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: 0,
        userName: name,
        password: password,
        email: email,
        image: "",
        dateOfBirth: new Date().toISOString(),
        nationality: "Egypt"
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Signup failed');
    }

    console.log('Signup success:', data);
    localStorage.setItem('user', JSON.stringify(data));

    if (setIsAuthenticated) setIsAuthenticated(true);
    navigate("/home");

  } catch (error) {
    console.error('Signup error:', error);
    alert('Signup failed: ' + error.message);
  }
};








  // const handleSubmit = (e) => {
  //   e.preventDefault();
  //   if (password !== confirmPassword) {
  //     alert("Passwords do not match!");
  //     return;
  //   }
  //   setIsAuthenticated(true);
  //   navigate("/home");
  // };



  // const handleSubmit = async (e) => {
  //   e.preventDefault();

  //   if (password !== confirmPassword) {
  //     alert("Passwords do not match!");
  //     return;
  //   }

  //   try {
  //     const response = await fetch('https://kotaby.duckdns.org/auth/signup', {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json'
  //       },
  //       body: JSON.stringify({
  //         userName: name,
  //         password: password,
  //         email: email
  //       })
  //     });

  //     if (!response.ok) {
  //       const errorData = await response.json();
  //       throw new Error(errorData.message || 'Signup failed');
  //     }

  //     const data = await response.json();
  //     console.log('Signup success:', data);

  //     if (setIsAuthenticated) setIsAuthenticated(true);
  //     navigate("/home");

  //   } catch (error) {
  //     console.error('Signup error:', error);
  //     alert('Signup failed: ' + error.message);
  //   }
  // };




  return (
    <div
      style={{
        minHeight: "100vh",
        backgroundImage: `url(${BgImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "40px",
        backdropFilter: "brightness(0.95)",
        position: "relative"
      }}
    >
      {/* Floating Lantern */}
      <img
        src={Lantern}
        alt="Lantern"
        style={{
          position: "absolute",
          top: "5%",
          left: "50%",
          transform: "translateX(-50%)",
          width: "60px",
          zIndex: 1,
          animation: "floatLantern 3s ease-in-out infinite"
        }}
      />

      <motion.form
        onSubmit={handleSubmit}
        initial={{ opacity: 0, scale: 0.9, rotateY: 15 }}
        animate={{ opacity: 1, scale: 1, rotateY: 0 }}
        exit={{ opacity: 0, scale: 0.9, rotateY: -15 }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
        style={{
          width: "100%",
          maxWidth: "420px",
          padding: "40px",
          borderRadius: "20px",
          background: "#fff",
          boxShadow: "0 0 15px rgba(212, 175, 55, 0.2)",
          border: "1px solid #f5e8b3",
          zIndex: 2,
          position: "relative"
        }}
      >
        <h2 style={{ textAlign: "center", marginBottom: "40px", color: "#d4af37" }}>
          Create An Account
        </h2>

        {/* Name */}
        <div style={{ marginBottom: "20px" }}>
          <label style={{ color: "#666", display: "block", marginBottom: "8px" }}>Name</label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            placeholder="Name"
            style={{
              width: "100%",
              padding: "12px",
              borderRadius: "10px",
              border: "1px solid #d4af37",
              outline: "none"
            }}
          />
        </div>

        {/* Email */}
        <div style={{ marginBottom: "20px" }}>
          <label style={{ color: "#666", display: "block", marginBottom: "8px" }}>Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            placeholder="Email"
            style={{
              width: "100%",
              padding: "12px",
              borderRadius: "10px",
              border: "1px solid #d4af37",
              outline: "none"
            }}
          />
        </div>

        {/* Password */}
        <div style={{ marginBottom: "20px", position: "relative" }}>
          <label style={{ color: "#666", display: "block", marginBottom: "8px" }}>Password</label>
          <input
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            placeholder="Password"
            style={{
              width: "100%",
              padding: "12px",
              borderRadius: "10px",
              border: "1px solid #d4af37",
              outline: "none"
            }}
          />
          <span
            onClick={() => setShowPassword(!showPassword)}
            style={{
              position: "absolute",
              right: "15px",
              top: "40px",
              cursor: "pointer",
              color: "#d4af37"
            }}
          >
            {showPassword ? <FaEyeSlash /> : <FaEye />}
          </span>
        </div>

        {/* Confirm Password */}
        <div style={{ marginBottom: "40px", position: "relative" }}>
          <label style={{ color: "#666", display: "block", marginBottom: "8px" }}>Confirm Password</label>
          <input
            type={showConfirmPassword ? "text" : "password"}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
            placeholder="Confirm Password"
            style={{
              width: "100%",
              padding: "12px",
              borderRadius: "10px",
              border: "1px solid #d4af37",
              outline: "none"
            }}
          />
          <span
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            style={{
              position: "absolute",
              right: "15px",
              top: "40px",
              cursor: "pointer",
              color: "#d4af37"
            }}
          >
            {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
          </span>
        </div>

        {/* Signup Button */}
        <button
          type="submit"
          style={{
            width: "100%",
            padding: "12px",
            borderRadius: "30px",
            background: "linear-gradient(135deg, #d4af37, #f9e79f)",
            border: "none",
            fontWeight: "bold",
            color: "#fff",
            fontSize: "16px",
            cursor: "pointer",
            transition: "all 0.3s"
          }}
          onMouseOver={(e) => (e.currentTarget.style.boxShadow = "0 0 8px rgba(212,175,55,0.3)")}
          onMouseOut={(e) => (e.currentTarget.style.boxShadow = "none")}
        >
          Sign Up
        </button>

        {/* Switch to login */}
        <p style={{ textAlign: "center", marginTop: "25px", color: "#000" }}>
          Have an account already?{" "}
          <Link to="/login" style={{ color: "#d4af37", fontWeight: "500", textDecoration: "none" }}>
            Log in
          </Link>
        </p>
      </motion.form>
    </div>
  );
}

export default Signup;
